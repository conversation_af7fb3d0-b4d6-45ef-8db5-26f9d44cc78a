/* Test Page Specific Styles */

/* Test Container */
.test-container {
    min-height: 100vh;
    padding-top: 80px;
}

.test-container section {
    display: none;
    min-height: calc(100vh - 80px);
}

.test-container section.active {
    display: block;
}

/* Welcome Screen */
.welcome-screen {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.welcome-screen::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="welcome-pattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23welcome-pattern)"/></svg>');
    opacity: 0.3;
}

.welcome-content {
    position: relative;
    z-index: 1;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    padding: var(--spacing-12) var(--spacing-6);
}

.welcome-title {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    color: var(--white);
    margin-bottom: var(--spacing-6);
}

.welcome-subtitle {
    font-size: var(--font-size-xl);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--spacing-12);
    line-height: 1.6;
}

.test-info {
    margin-bottom: var(--spacing-12);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.info-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-item i {
    font-size: var(--font-size-2xl);
    color: var(--white);
    flex-shrink: 0;
}

.info-item h3 {
    color: var(--white);
    margin: 0 0 var(--spacing-1) 0;
    font-size: var(--font-size-lg);
}

.info-item p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-size: var(--font-size-sm);
}

.instructions {
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    margin-bottom: var(--spacing-12);
    text-align: left;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.instructions h3 {
    color: var(--white);
    margin-bottom: var(--spacing-4);
    text-align: center;
}

.instructions ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.instructions li {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-3);
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
}

.instructions li::before {
    content: '✓';
    color: var(--accent-color);
    font-weight: bold;
    flex-shrink: 0;
    margin-top: 2px;
}

.welcome-actions {
    text-align: center;
}

.privacy-note {
    margin-top: var(--spacing-6);
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
}

/* Question Screen */
.question-screen {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: var(--spacing-8) 0;
    position: relative;
    overflow: hidden;
}

.question-screen::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(99, 102, 241, 0.03) 0%, transparent 70%);
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(120deg); }
    66% { transform: translate(-20px, 20px) rotate(240deg); }
}

.progress-container {
    margin-bottom: var(--spacing-8);
    background: var(--white);
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
}

.progress-text {
    font-weight: 600;
    color: var(--gray-700);
}

.progress-percentage {
    font-weight: 700;
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-full);
    transition: width var(--transition-normal);
    width: 2%;
}

.question-content {
    max-width: 800px;
    margin: 0 auto;
}

.question-card {
    background: var(--white);
    padding: var(--spacing-12);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
    animation: slideInUp 0.6s ease-out;
    transform-origin: center bottom;
}

.question-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--primary-light));
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.question-header {
    text-align: center;
    margin-bottom: var(--spacing-10);
}

.question-text {
    font-size: var(--font-size-2xl);
    color: var(--gray-900);
    line-height: 1.4;
    margin: 0;
}

.answer-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-10);
}

.answer-option {
    background: var(--gray-50);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    animation: fadeInScale 0.4s ease-out forwards;
    opacity: 0;
    transform: scale(0.95);
}

.answer-option:nth-child(1) { animation-delay: 0.1s; }
.answer-option:nth-child(2) { animation-delay: 0.2s; }
.answer-option:nth-child(3) { animation-delay: 0.3s; }
.answer-option:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInScale {
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.answer-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.15), transparent);
    transition: left var(--transition-slow);
}

.answer-option:hover::before {
    left: 100%;
}

.answer-option:hover {
    border-color: var(--primary-color);
    background: var(--white);
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 10px 25px rgba(99, 102, 241, 0.15);
}

.answer-option.selected {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    transform: translateY(-2px) scale(1.01);
    box-shadow: 0 8px 20px rgba(99, 102, 241, 0.3);
    animation: selectedPulse 0.6s ease-out;
}

@keyframes selectedPulse {
    0% { transform: translateY(-2px) scale(1.01); }
    50% { transform: translateY(-4px) scale(1.05); }
    100% { transform: translateY(-2px) scale(1.01); }
}

.answer-option.selected:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-4px) scale(1.02);
}

.option-text {
    font-size: var(--font-size-lg);
    font-weight: 500;
    margin: 0;
    position: relative;
    z-index: 1;
}

.question-actions {
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-4);
}

.question-actions .btn {
    flex: 1;
    max-width: 200px;
}

/* Loading Screen */
.loading-screen {
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-content {
    text-align: center;
    max-width: 600px;
    padding: var(--spacing-12);
}

.loading-animation {
    margin-bottom: var(--spacing-8);
    position: relative;
}

.brain-icon {
    font-size: 80px;
    color: var(--primary-color);
    margin-bottom: var(--spacing-6);
    animation: pulse 2s ease-in-out infinite;
}

.loading-dots {
    display: flex;
    justify-content: center;
    gap: var(--spacing-2);
}

.loading-dots span {
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.7; }
}

@keyframes bounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

.loading-title {
    font-size: var(--font-size-3xl);
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.loading-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    margin-bottom: var(--spacing-8);
    line-height: 1.6;
}

.loading-steps {
    display: flex;
    justify-content: center;
    gap: var(--spacing-6);
    flex-wrap: wrap;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-4);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    opacity: 0.5;
}

.step.active {
    opacity: 1;
    background: var(--primary-color);
    color: var(--white);
    transform: scale(1.05);
}

.step i {
    font-size: var(--font-size-xl);
}

.step span {
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* Results Screen */
.results-screen {
    background: var(--gray-50);
    padding: var(--spacing-8) 0;
}

.results-content {
    max-width: 1000px;
    margin: 0 auto;
}

.results-header {
    text-align: center;
    margin-bottom: var(--spacing-16);
    background: var(--white);
    padding: var(--spacing-12);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
}

.personality-badge {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    padding: var(--spacing-10);
    border-radius: var(--radius-2xl);
    margin-bottom: var(--spacing-8);
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
    animation: badgeEntrance 1s ease-out;
    transform-origin: center;
}

.personality-badge::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: rotate 10s linear infinite;
}

.personality-badge::after {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 3s ease-in-out infinite;
}

@keyframes badgeEntrance {
    0% {
        opacity: 0;
        transform: scale(0.5) rotate(-10deg);
    }
    50% {
        transform: scale(1.1) rotate(5deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.type-code {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    letter-spacing: 4px;
    margin-bottom: var(--spacing-2);
}

.type-name {
    font-size: var(--font-size-xl);
    font-weight: 500;
    opacity: 0.9;
}

.results-title {
    font-size: var(--font-size-4xl);
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.results-subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto var(--spacing-8) auto;
}

.personality-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-8);
    flex-wrap: wrap;
    margin-top: var(--spacing-6);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    background: rgba(99, 102, 241, 0.1);
    border-radius: var(--radius-full);
    color: var(--primary-color);
    font-size: var(--font-size-sm);
    font-weight: 500;
    animation: statFadeIn 0.6s ease-out both;
    animation-delay: calc(var(--i) * 0.2s);
}

.stat-item:nth-child(1) { --i: 1; }
.stat-item:nth-child(2) { --i: 2; }
.stat-item:nth-child(3) { --i: 3; }

@keyframes statFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.stat-item i {
    font-size: var(--font-size-base);
}

/* Dimensions Section */
.dimensions-section,
.traits-section,
.share-section {
    background: var(--white);
    padding: var(--spacing-10);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-8);
}

.section-title {
    font-size: var(--font-size-2xl);
    color: var(--gray-900);
    margin-bottom: var(--spacing-8);
    text-align: center;
}

.dimensions-grid {
    display: grid;
    gap: var(--spacing-6);
}

.dimension-item {
    padding: var(--spacing-6);
    background: var(--gray-50);
    border-radius: var(--radius-xl);
    border: 2px solid transparent;
    transition: all var(--transition-normal);
}

.dimension-item:hover {
    border-color: var(--primary-color);
    background: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.dimension-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
}

.dimension-label {
    font-weight: 600;
    color: var(--gray-700);
}

.dimension-result {
    font-weight: 700;
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.dimension-bar {
    width: 100%;
    height: 12px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--spacing-4);
}

.bar-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-full);
    transition: width var(--transition-slow);
}

.dimension-description {
    color: var(--gray-600);
    line-height: 1.6;
    font-size: var(--font-size-sm);
}

/* Traits Section */
.traits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-6);
}

.trait-item {
    text-align: center;
    padding: var(--spacing-6);
    background: var(--gray-50);
    border-radius: var(--radius-xl);
    transition: all var(--transition-normal);
    border: 2px solid transparent;
}

.trait-item:hover {
    border-color: var(--primary-color);
    background: var(--white);
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.trait-item i {
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-4);
}

.trait-item h3 {
    font-size: var(--font-size-lg);
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
}

.trait-item p {
    color: var(--gray-600);
    line-height: 1.6;
    margin: 0;
}

/* Share Section */
.share-options {
    display: flex;
    justify-content: center;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-8);
    flex-wrap: wrap;
}

.social-share {
    text-align: center;
}

.social-share p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-4);
}

.social-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-3);
}

.social-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    color: var(--white);
    font-size: var(--font-size-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.social-btn.twitter { background: #1da1f2; }
.social-btn.facebook { background: #4267b2; }
.social-btn.linkedin { background: #0077b5; }
.social-btn.whatsapp { background: #25d366; }

/* Results Actions */
.results-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-6);
    margin-top: var(--spacing-12);
    flex-wrap: wrap;
}

/* Share Image Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: var(--spacing-4);
}

.modal.active {
    display: flex;
}

.modal-overlay {
    background: rgba(0, 0, 0, 0.5);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-4);
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius-2xl);
    max-width: 900px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    z-index: 1;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
    margin: 0;
    color: var(--gray-900);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    cursor: pointer;
    color: var(--gray-500);
    padding: var(--spacing-2);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.modal-body {
    padding: var(--spacing-6);
}

.share-image-container {
    text-align: center;
    margin-bottom: var(--spacing-6);
}

#shareCanvas {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.modal-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-4);
    flex-wrap: wrap;
}

/* Personality Type Specific Styles */
.personality-badge.INTJ { background: linear-gradient(135deg, #4c1d95, #7c3aed); }
.personality-badge.INTP { background: linear-gradient(135deg, #1e40af, #3b82f6); }
.personality-badge.ENTJ { background: linear-gradient(135deg, #dc2626, #ef4444); }
.personality-badge.ENTP { background: linear-gradient(135deg, #ea580c, #f97316); }
.personality-badge.INFJ { background: linear-gradient(135deg, #059669, #10b981); }
.personality-badge.INFP { background: linear-gradient(135deg, #7c2d12, #ea580c); }
.personality-badge.ENFJ { background: linear-gradient(135deg, #be185d, #ec4899); }
.personality-badge.ENFP { background: linear-gradient(135deg, #c2410c, #f97316); }
.personality-badge.ISTJ { background: linear-gradient(135deg, #374151, #6b7280); }
.personality-badge.ISFJ { background: linear-gradient(135deg, #0f766e, #14b8a6); }
.personality-badge.ESTJ { background: linear-gradient(135deg, #991b1b, #dc2626); }
.personality-badge.ESFJ { background: linear-gradient(135deg, #be123c, #e11d48); }
.personality-badge.ISTP { background: linear-gradient(135deg, #365314, #65a30d); }
.personality-badge.ISFP { background: linear-gradient(135deg, #a21caf, #c026d3); }
.personality-badge.ESTP { background: linear-gradient(135deg, #b45309, #d97706); }
.personality-badge.ESFP { background: linear-gradient(135deg, #db2777, #f472b6); }

/* Personality Type Icons */
.personality-badge .type-icon {
    position: absolute;
    top: 15px;
    left: 15px;
    font-size: var(--font-size-2xl);
    opacity: 0.3;
    z-index: 1;
}

.personality-badge.INTJ .type-icon::before { content: '🏗️'; }
.personality-badge.INTP .type-icon::before { content: '🔬'; }
.personality-badge.ENTJ .type-icon::before { content: '👑'; }
.personality-badge.ENTP .type-icon::before { content: '💡'; }
.personality-badge.INFJ .type-icon::before { content: '🌟'; }
.personality-badge.INFP .type-icon::before { content: '🎨'; }
.personality-badge.ENFJ .type-icon::before { content: '🎭'; }
.personality-badge.ENFP .type-icon::before { content: '🎪'; }
.personality-badge.ISTJ .type-icon::before { content: '📋'; }
.personality-badge.ISFJ .type-icon::before { content: '🛡️'; }
.personality-badge.ESTJ .type-icon::before { content: '⚖️'; }
.personality-badge.ESFJ .type-icon::before { content: '🤝'; }
.personality-badge.ISTP .type-icon::before { content: '🔧'; }
.personality-badge.ISFP .type-icon::before { content: '🌸'; }
.personality-badge.ESTP .type-icon::before { content: '🏃'; }
.personality-badge.ESFP .type-icon::before { content: '🎉'; }

/* Enhanced Share Section */
.share-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
    overflow: hidden;
    text-align: center;
}

.share-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    margin-bottom: var(--spacing-8);
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.btn-glow {
    position: relative;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
    overflow: hidden;
}

.btn-glow .btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shine 2s ease-in-out infinite;
}

@keyframes shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.share-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-8);
    margin: var(--spacing-8) 0;
    flex-wrap: wrap;
}

.share-stat {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    padding: var(--spacing-2) var(--spacing-4);
    background: rgba(99, 102, 241, 0.05);
    border-radius: var(--radius-full);
    animation: statPulse 2s ease-in-out infinite;
}

@keyframes statPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.share-stat i {
    color: var(--primary-color);
}

.social-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-6);
}

.social-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-4);
    flex-wrap: wrap;
    margin-bottom: var(--spacing-8);
}

.social-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    color: var(--white);
    font-size: var(--font-size-xl);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.social-btn .social-label {
    font-size: 10px;
    font-weight: 500;
    margin-top: 2px;
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.social-btn:hover .social-label {
    opacity: 1;
}

.social-btn:hover {
    transform: translateY(-4px) scale(1.1);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.social-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.social-btn:hover::before {
    opacity: 1;
}

.social-btn.twitter { background: linear-gradient(135deg, #1da1f2, #0d8bd9); }
.social-btn.facebook { background: linear-gradient(135deg, #4267b2, #365899); }
.social-btn.linkedin { background: linear-gradient(135deg, #0077b5, #005885); }
.social-btn.whatsapp { background: linear-gradient(135deg, #25d366, #1ebe57); }
.social-btn.instagram { background: linear-gradient(135deg, #e4405f, #833ab4, #fcb045); }
.social-btn.tiktok { background: linear-gradient(135deg, #000000, #ff0050); }

.share-inspiration {
    background: rgba(255, 255, 255, 0.7);
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    margin-top: var(--spacing-8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.share-inspiration h3 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-lg);
}

.inspiration-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-3);
    justify-content: center;
}

.inspiration-tag {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.inspiration-tag::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left var(--transition-normal);
}

.inspiration-tag:hover::before {
    left: 100%;
}

.inspiration-tag:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 5px 15px rgba(99, 102, 241, 0.3);
}

.share-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="share-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(99,102,241,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23share-pattern)"/></svg>');
    opacity: 0.5;
}

.share-options {
    position: relative;
    z-index: 1;
}

.share-options .btn {
    position: relative;
    overflow: hidden;
    transform: translateY(0);
    transition: all var(--transition-normal);
}

.share-options .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.share-options .btn:hover::before {
    left: 100%;
}

.share-options .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Social Share Animation */
.social-buttons {
    animation: socialFadeIn 0.8s ease-out 0.5s both;
}

@keyframes socialFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.social-btn {
    animation: socialBounce 0.6s ease-out both;
    animation-delay: calc(var(--i) * 0.1s);
}

.social-btn:nth-child(1) { --i: 1; }
.social-btn:nth-child(2) { --i: 2; }
.social-btn:nth-child(3) { --i: 3; }
.social-btn:nth-child(4) { --i: 4; }

@keyframes socialBounce {
    0% {
        opacity: 0;
        transform: scale(0) rotate(180deg);
    }
    50% {
        transform: scale(1.2) rotate(0deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}

/* Enhanced Traits Section */
.trait-item {
    animation: traitSlideIn 0.6s ease-out both;
    animation-delay: calc(var(--i) * 0.1s);
}

.trait-item:nth-child(1) { --i: 1; }
.trait-item:nth-child(2) { --i: 2; }
.trait-item:nth-child(3) { --i: 3; }
.trait-item:nth-child(4) { --i: 4; }

@keyframes traitSlideIn {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.trait-item:hover {
    animation: traitHover 0.3s ease-out;
}

@keyframes traitHover {
    0% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-8px) scale(1.05); }
    100% { transform: translateY(-4px) scale(1.02); }
}

/* Additional Animation Keyframes */
@keyframes confettiFall {
    0% {
        transform: translateY(-10px) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(720deg);
        opacity: 0;
    }
}

@keyframes sparkle {
    0% {
        opacity: 0;
        transform: translateX(-50%) scale(0.5);
    }
    50% {
        opacity: 1;
        transform: translateX(-50%) scale(1.2);
    }
    100% {
        opacity: 0;
        transform: translateX(-50%) scale(0.8) translateY(-20px);
    }
}

@keyframes feedbackSlideIn {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(-40px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateX(-50%) translateY(-20px) scale(1);
    }
}

@keyframes feedbackSlideOut {
    0% {
        opacity: 1;
        transform: translateX(-50%) translateY(-20px) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateX(-50%) translateY(-60px) scale(0.9);
    }
}

@keyframes floatUp {
    0% {
        opacity: 0;
        transform: translateY(0) scale(0.5) rotate(0deg);
    }
    20% {
        opacity: 1;
        transform: translateY(-20px) scale(1) rotate(90deg);
    }
    100% {
        opacity: 0;
        transform: translateY(-100vh) scale(0.3) rotate(360deg);
    }
}

@keyframes questionSlideIn {
    0% {
        opacity: 0;
        transform: translateX(100px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes questionSlideOut {
    0% {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateX(-100px) scale(0.95);
    }
}

/* Enhanced Progress Bar Animation */
.progress-fill {
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--primary-light));
    background-size: 200% 100%;
    animation: progressShimmer 2s ease-in-out infinite;
}

@keyframes progressShimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Question Transition Effects */
.question-card.transitioning-out {
    animation: questionSlideOut 0.3s ease-in forwards;
}

.question-card.transitioning-in {
    animation: questionSlideIn 0.3s ease-out forwards;
}

/* Enhanced Loading Animation */
.loading-animation {
    position: relative;
}

.loading-animation::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120px;
    height: 120px;
    border: 3px solid rgba(99, 102, 241, 0.1);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Personality Badge Hover Effects */
.personality-badge:hover {
    transform: scale(1.05) rotate(2deg);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.personality-badge:hover::after {
    animation: float 1s ease-in-out infinite;
}

/* Enhanced Modal Animations */
.modal.active {
    animation: modalFadeIn 0.3s ease-out;
}

.modal.active .modal-content {
    animation: modalSlideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.7) translateY(50px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Share Button Pulse Effect */
.share-options .btn:active {
    animation: buttonPulse 0.3s ease-out;
}

@keyframes buttonPulse {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

/* Results Screen Entrance */
.results-screen.active {
    animation: resultsEntrance 0.8s ease-out;
}

@keyframes resultsEntrance {
    0% {
        opacity: 0;
        transform: translateY(50px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes rippleEffect {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
    }
    100% {
        width: 200px;
        height: 200px;
        opacity: 0;
    }
}

@keyframes sparkleFloat {
    0% {
        opacity: 0;
        transform: translateY(0) scale(0.5);
    }
    50% {
        opacity: 1;
        transform: translateY(-10px) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-20px) scale(0.3);
    }
}

@keyframes progressCelebration {
    0% { transform: scaleY(1); }
    50% { transform: scaleY(1.5); }
    100% { transform: scaleY(1); }
}

/* Enhanced Question Text Animation */
.question-text {
    position: relative;
    overflow: hidden;
}

.question-text::after {
    content: '|';
    animation: blink 1s infinite;
    color: var(--primary-color);
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Answer Option Hover Glow Effect */
.answer-option:hover {
    box-shadow: 0 10px 25px rgba(99, 102, 241, 0.15), 0 0 20px rgba(99, 102, 241, 0.1);
}

.answer-option.selected {
    box-shadow: 0 8px 20px rgba(99, 102, 241, 0.3), 0 0 30px rgba(99, 102, 241, 0.2);
}

/* Loading Screen Enhancements */
.loading-screen {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.loading-screen::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="loading-pattern" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23loading-pattern)"/></svg>');
    animation: patternMove 20s linear infinite;
}

@keyframes patternMove {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(25px) translateY(25px); }
}

.loading-content {
    position: relative;
    z-index: 1;
}

/* Enhanced Step Animation */
.step {
    position: relative;
    overflow: hidden;
}

.step::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.step.active::before {
    left: 100%;
}

/* Welcome Screen Enhancements */
.info-item:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Navigation Button Enhancements */
.question-actions .btn {
    position: relative;
    overflow: hidden;
}

.question-actions .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.3s ease;
}

.question-actions .btn:hover::before {
    left: 100%;
}

.question-actions .btn:disabled {
    opacity: 0.5;
    transform: scale(0.95);
}

.question-actions .btn:not(:disabled) {
    transform: scale(1);
    transition: all 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
    .welcome-title {
        font-size: var(--font-size-4xl);
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .info-item {
        flex-direction: column;
        text-align: center;
    }
    
    .question-card {
        padding: var(--spacing-8);
    }
    
    .question-text {
        font-size: var(--font-size-xl);
    }
    
    .question-actions {
        flex-direction: column;
    }
    
    .question-actions .btn {
        max-width: none;
    }
    
    .type-code {
        font-size: var(--font-size-4xl);
    }
    
    .results-title {
        font-size: var(--font-size-3xl);
    }
    
    .traits-grid {
        grid-template-columns: 1fr;
    }
    
    .share-options {
        flex-direction: column;
        align-items: center;
    }
    
    .results-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .loading-steps {
        flex-direction: column;
        align-items: center;
    }
}
