/* Test Page Specific Styles */

/* Test Container */
.test-container {
    min-height: 100vh;
    padding-top: 80px;
}

.test-container section {
    display: none;
    min-height: calc(100vh - 80px);
}

.test-container section.active {
    display: block;
}

/* Welcome Screen */
.welcome-screen {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.welcome-screen::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="welcome-pattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23welcome-pattern)"/></svg>');
    opacity: 0.3;
}

.welcome-content {
    position: relative;
    z-index: 1;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    padding: var(--spacing-12) var(--spacing-6);
}

.welcome-title {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    color: var(--white);
    margin-bottom: var(--spacing-6);
}

.welcome-subtitle {
    font-size: var(--font-size-xl);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--spacing-12);
    line-height: 1.6;
}

.test-info {
    margin-bottom: var(--spacing-12);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.info-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-item i {
    font-size: var(--font-size-2xl);
    color: var(--white);
    flex-shrink: 0;
}

.info-item h3 {
    color: var(--white);
    margin: 0 0 var(--spacing-1) 0;
    font-size: var(--font-size-lg);
}

.info-item p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-size: var(--font-size-sm);
}

.instructions {
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    margin-bottom: var(--spacing-12);
    text-align: left;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.instructions h3 {
    color: var(--white);
    margin-bottom: var(--spacing-4);
    text-align: center;
}

.instructions ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.instructions li {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-3);
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
}

.instructions li::before {
    content: '✓';
    color: var(--accent-color);
    font-weight: bold;
    flex-shrink: 0;
    margin-top: 2px;
}

.welcome-actions {
    text-align: center;
}

.privacy-note {
    margin-top: var(--spacing-6);
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
}

/* Question Screen */
.question-screen {
    background: var(--gray-50);
    padding: var(--spacing-8) 0;
}

.progress-container {
    margin-bottom: var(--spacing-8);
    background: var(--white);
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
}

.progress-text {
    font-weight: 600;
    color: var(--gray-700);
}

.progress-percentage {
    font-weight: 700;
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-full);
    transition: width var(--transition-normal);
    width: 2%;
}

.question-content {
    max-width: 800px;
    margin: 0 auto;
}

.question-card {
    background: var(--white);
    padding: var(--spacing-12);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
}

.question-header {
    text-align: center;
    margin-bottom: var(--spacing-10);
}

.question-text {
    font-size: var(--font-size-2xl);
    color: var(--gray-900);
    line-height: 1.4;
    margin: 0;
}

.answer-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-10);
}

.answer-option {
    background: var(--gray-50);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.answer-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
    transition: left var(--transition-slow);
}

.answer-option:hover::before {
    left: 100%;
}

.answer-option:hover {
    border-color: var(--primary-color);
    background: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.answer-option.selected {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: var(--white);
}

.answer-option.selected:hover {
    background: var(--primary-dark);
}

.option-text {
    font-size: var(--font-size-lg);
    font-weight: 500;
    margin: 0;
    position: relative;
    z-index: 1;
}

.question-actions {
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-4);
}

.question-actions .btn {
    flex: 1;
    max-width: 200px;
}

/* Loading Screen */
.loading-screen {
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-content {
    text-align: center;
    max-width: 600px;
    padding: var(--spacing-12);
}

.loading-animation {
    margin-bottom: var(--spacing-8);
    position: relative;
}

.brain-icon {
    font-size: 80px;
    color: var(--primary-color);
    margin-bottom: var(--spacing-6);
    animation: pulse 2s ease-in-out infinite;
}

.loading-dots {
    display: flex;
    justify-content: center;
    gap: var(--spacing-2);
}

.loading-dots span {
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.7; }
}

@keyframes bounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

.loading-title {
    font-size: var(--font-size-3xl);
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.loading-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    margin-bottom: var(--spacing-8);
    line-height: 1.6;
}

.loading-steps {
    display: flex;
    justify-content: center;
    gap: var(--spacing-6);
    flex-wrap: wrap;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-4);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    opacity: 0.5;
}

.step.active {
    opacity: 1;
    background: var(--primary-color);
    color: var(--white);
    transform: scale(1.05);
}

.step i {
    font-size: var(--font-size-xl);
}

.step span {
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* Results Screen */
.results-screen {
    background: var(--gray-50);
    padding: var(--spacing-8) 0;
}

.results-content {
    max-width: 1000px;
    margin: 0 auto;
}

.results-header {
    text-align: center;
    margin-bottom: var(--spacing-16);
    background: var(--white);
    padding: var(--spacing-12);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
}

.personality-badge {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-2xl);
    margin-bottom: var(--spacing-8);
    box-shadow: var(--shadow-xl);
}

.type-code {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    letter-spacing: 4px;
    margin-bottom: var(--spacing-2);
}

.type-name {
    font-size: var(--font-size-xl);
    font-weight: 500;
    opacity: 0.9;
}

.results-title {
    font-size: var(--font-size-4xl);
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.results-subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

/* Dimensions Section */
.dimensions-section,
.traits-section,
.share-section {
    background: var(--white);
    padding: var(--spacing-10);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-8);
}

.section-title {
    font-size: var(--font-size-2xl);
    color: var(--gray-900);
    margin-bottom: var(--spacing-8);
    text-align: center;
}

.dimensions-grid {
    display: grid;
    gap: var(--spacing-6);
}

.dimension-item {
    padding: var(--spacing-6);
    background: var(--gray-50);
    border-radius: var(--radius-xl);
    border: 2px solid transparent;
    transition: all var(--transition-normal);
}

.dimension-item:hover {
    border-color: var(--primary-color);
    background: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.dimension-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
}

.dimension-label {
    font-weight: 600;
    color: var(--gray-700);
}

.dimension-result {
    font-weight: 700;
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.dimension-bar {
    width: 100%;
    height: 12px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--spacing-4);
}

.bar-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-full);
    transition: width var(--transition-slow);
}

.dimension-description {
    color: var(--gray-600);
    line-height: 1.6;
    font-size: var(--font-size-sm);
}

/* Traits Section */
.traits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-6);
}

.trait-item {
    text-align: center;
    padding: var(--spacing-6);
    background: var(--gray-50);
    border-radius: var(--radius-xl);
    transition: all var(--transition-normal);
    border: 2px solid transparent;
}

.trait-item:hover {
    border-color: var(--primary-color);
    background: var(--white);
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.trait-item i {
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-4);
}

.trait-item h3 {
    font-size: var(--font-size-lg);
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
}

.trait-item p {
    color: var(--gray-600);
    line-height: 1.6;
    margin: 0;
}

/* Share Section */
.share-options {
    display: flex;
    justify-content: center;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-8);
    flex-wrap: wrap;
}

.social-share {
    text-align: center;
}

.social-share p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-4);
}

.social-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-3);
}

.social-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    color: var(--white);
    font-size: var(--font-size-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.social-btn.twitter { background: #1da1f2; }
.social-btn.facebook { background: #4267b2; }
.social-btn.linkedin { background: #0077b5; }
.social-btn.whatsapp { background: #25d366; }

/* Results Actions */
.results-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-6);
    margin-top: var(--spacing-12);
    flex-wrap: wrap;
}

/* Share Image Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: var(--spacing-4);
}

.modal.active {
    display: flex;
}

.modal-overlay {
    background: rgba(0, 0, 0, 0.5);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-4);
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius-2xl);
    max-width: 900px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    z-index: 1;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
    margin: 0;
    color: var(--gray-900);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    cursor: pointer;
    color: var(--gray-500);
    padding: var(--spacing-2);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.modal-body {
    padding: var(--spacing-6);
}

.share-image-container {
    text-align: center;
    margin-bottom: var(--spacing-6);
}

#shareCanvas {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.modal-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-4);
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
    .welcome-title {
        font-size: var(--font-size-4xl);
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .info-item {
        flex-direction: column;
        text-align: center;
    }
    
    .question-card {
        padding: var(--spacing-8);
    }
    
    .question-text {
        font-size: var(--font-size-xl);
    }
    
    .question-actions {
        flex-direction: column;
    }
    
    .question-actions .btn {
        max-width: none;
    }
    
    .type-code {
        font-size: var(--font-size-4xl);
    }
    
    .results-title {
        font-size: var(--font-size-3xl);
    }
    
    .traits-grid {
        grid-template-columns: 1fr;
    }
    
    .share-options {
        flex-direction: column;
        align-items: center;
    }
    
    .results-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .loading-steps {
        flex-direction: column;
        align-items: center;
    }
}
