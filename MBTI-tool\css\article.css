/* Article Page Specific Styles */

/* Article Header */
.article-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 120px 0 60px;
    position: relative;
    overflow: hidden;
}

.article-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="article-pattern" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23article-pattern)"/></svg>');
    opacity: 0.3;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-8);
    font-size: var(--font-size-sm);
    position: relative;
    z-index: 1;
}

.breadcrumb a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.breadcrumb a:hover {
    color: var(--white);
}

.breadcrumb i {
    color: rgba(255, 255, 255, 0.6);
    font-size: var(--font-size-xs);
}

.breadcrumb span {
    color: var(--white);
}

.article-meta-header {
    position: relative;
    z-index: 1;
    max-width: 800px;
}

.article-category {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--spacing-6);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.article-title {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    color: var(--white);
    margin-bottom: var(--spacing-6);
    line-height: 1.1;
}

.article-subtitle {
    font-size: var(--font-size-xl);
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin-bottom: var(--spacing-8);
}

.article-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-4);
}

.author-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.author-avatar {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-xl);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.author-details {
    display: flex;
    flex-direction: column;
}

.author-name {
    color: var(--white);
    font-weight: 600;
    font-size: var(--font-size-base);
}

.publish-date {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-sm);
}

.article-stats {
    display: flex;
    gap: var(--spacing-6);
}

.article-stats span {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-sm);
}

/* Article Content */
.article-content {
    padding: var(--spacing-20) 0;
    background: var(--white);
}

.content-wrapper {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-12);
    max-width: 1200px;
    margin: 0 auto;
}

@media (min-width: 1024px) {
    .content-wrapper {
        grid-template-columns: 250px 1fr;
    }
}

/* Table of Contents Sidebar */
.toc-sidebar {
    order: 2;
}

@media (min-width: 1024px) {
    .toc-sidebar {
        order: 1;
    }
}

.toc-container {
    background: var(--gray-50);
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    border: 1px solid var(--gray-200);
    position: sticky;
    top: 100px;
}

.toc-container h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-4);
    color: var(--gray-900);
}

.toc-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.toc-list li {
    margin-bottom: var(--spacing-2);
}

.toc-list a {
    display: block;
    padding: var(--spacing-2) var(--spacing-3);
    color: var(--gray-600);
    text-decoration: none;
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
    border-left: 3px solid transparent;
}

.toc-list a:hover,
.toc-list a.active {
    background: var(--white);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
    transform: translateX(4px);
}

/* Main Article Content */
.article-main {
    order: 1;
}

@media (min-width: 1024px) {
    .article-main {
        order: 2;
    }
}

.article-image {
    margin-bottom: var(--spacing-8);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.article-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.image-caption {
    background: var(--gray-800);
    color: var(--white);
    padding: var(--spacing-4);
    font-size: var(--font-size-sm);
    text-align: center;
    font-style: italic;
}

.article-body {
    background: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    border: 1px solid var(--gray-200);
    box-shadow: var(--shadow-sm);
}

.article-body section {
    margin-bottom: var(--spacing-12);
    padding-bottom: var(--spacing-8);
    border-bottom: 1px solid var(--gray-200);
}

.article-body section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.article-body h2 {
    font-size: var(--font-size-2xl);
    color: var(--gray-900);
    margin-bottom: var(--spacing-6);
    padding-bottom: var(--spacing-3);
    border-bottom: 2px solid var(--primary-color);
    position: relative;
}

.article-body h2::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: var(--primary-dark);
}

.article-body h3 {
    font-size: var(--font-size-xl);
    color: var(--gray-800);
    margin: var(--spacing-8) 0 var(--spacing-4);
}

.article-body p {
    color: var(--gray-700);
    line-height: 1.7;
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-base);
}

.article-body ul,
.article-body ol {
    margin: var(--spacing-4) 0;
    padding-left: var(--spacing-6);
}

.article-body li {
    color: var(--gray-700);
    line-height: 1.6;
    margin-bottom: var(--spacing-2);
}

.article-body a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-fast);
}

.article-body a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Highlight Box */
.highlight-box {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    margin: var(--spacing-6) 0;
    position: relative;
    overflow: hidden;
}

.highlight-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="highlight-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23highlight-pattern)"/></svg>');
    opacity: 0.3;
}

.highlight-box * {
    position: relative;
    z-index: 1;
}

.highlight-box h3 {
    color: var(--white);
    margin-top: 0;
    margin-bottom: var(--spacing-4);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.highlight-box p {
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 0;
}

/* Dimension Grid */
.dimension-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
    margin: var(--spacing-8) 0;
}

@media (min-width: 768px) {
    .dimension-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

.dimension-card {
    background: var(--gray-50);
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    border: 2px solid transparent;
    transition: all var(--transition-normal);
}

.dimension-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.dimension-card h3 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: var(--spacing-3);
    font-size: var(--font-size-lg);
}

.dimension-card p {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-4);
}

.dimension-card ul {
    margin: 0;
    padding-left: var(--spacing-4);
}

.dimension-card li {
    margin-bottom: var(--spacing-2);
}

/* Type Group */
.type-group {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
    margin: var(--spacing-8) 0;
}

@media (min-width: 768px) {
    .type-group {
        grid-template-columns: repeat(2, 1fr);
    }
}

.type-card {
    background: var(--white);
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    border: 2px solid var(--gray-200);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.type-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.05), transparent);
    transition: left var(--transition-slow);
}

.type-card:hover::before {
    left: 100%;
}

.type-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.type-card h3 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: var(--spacing-4);
    position: relative;
    z-index: 1;
}

.type-card p {
    margin-bottom: var(--spacing-4);
    position: relative;
    z-index: 1;
}

.type-traits {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-2);
    position: relative;
    z-index: 1;
}

.trait {
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

/* Tips List */
.tips-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
    margin: var(--spacing-8) 0;
}

.tip-item {
    display: flex;
    gap: var(--spacing-4);
    padding: var(--spacing-6);
    background: var(--gray-50);
    border-radius: var(--radius-xl);
    border-left: 4px solid var(--primary-color);
    transition: all var(--transition-normal);
}

.tip-item:hover {
    background: var(--white);
    box-shadow: var(--shadow-md);
    transform: translateX(4px);
}

.tip-item i {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
    margin-top: var(--spacing-1);
    flex-shrink: 0;
}

.tip-item h4 {
    margin: 0 0 var(--spacing-2) 0;
    color: var(--gray-900);
    font-size: var(--font-size-lg);
}

.tip-item p {
    margin: 0;
    color: var(--gray-600);
}

/* CTA Box */
.cta-box {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-2xl);
    text-align: center;
    margin: var(--spacing-8) 0;
    position: relative;
    overflow: hidden;
}

.cta-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="cta-pattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23cta-pattern)"/></svg>');
    opacity: 0.3;
}

.cta-box * {
    position: relative;
    z-index: 1;
}

.cta-box h3 {
    color: var(--white);
    margin-top: 0;
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-2xl);
}

.cta-box p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--spacing-6);
    font-size: var(--font-size-lg);
}

.cta-box .btn {
    background: var(--white);
    color: var(--primary-color);
}

.cta-box .btn:hover {
    background: var(--gray-100);
    color: var(--primary-dark);
}

/* Related Articles */
.related-articles {
    padding: var(--spacing-16) 0;
    background: var(--gray-50);
}

.related-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
}

@media (min-width: 768px) {
    .related-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

.related-article {
    background: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.related-article:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.related-article .article-thumbnail {
    height: 200px;
    overflow: hidden;
}

.related-article .article-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.related-article:hover .article-thumbnail img {
    transform: scale(1.05);
}

.related-article .article-info {
    padding: var(--spacing-6);
}

.related-article h3 {
    margin: 0 0 var(--spacing-3) 0;
    font-size: var(--font-size-lg);
}

.related-article h3 a {
    color: var(--gray-900);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.related-article h3 a:hover {
    color: var(--primary-color);
}

.related-article p {
    margin: 0;
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
    .article-title {
        font-size: var(--font-size-4xl);
    }
    
    .article-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .article-info {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .toc-container {
        position: static;
        margin-bottom: var(--spacing-8);
    }
    
    .article-body {
        padding: var(--spacing-6);
    }
    
    .dimension-grid,
    .type-group {
        grid-template-columns: 1fr;
    }
    
    .tip-item {
        flex-direction: column;
        text-align: center;
    }
    
    .tip-item i {
        margin: 0 auto var(--spacing-3);
    }
}
