// Blog page specific JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeBlogAnimations();
    initializeNewsletterForm();
    initializeLoadMore();
    initializeCategoryCards();
    initializeArticleInteractions();
});

// Initialize blog page animations
function initializeBlogAnimations() {
    const animatedElements = document.querySelectorAll('.featured-article, .category-card, .article-item');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.classList.add('animate-in');
                }, index * 100);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    animatedElements.forEach(element => {
        element.classList.add('animate-on-scroll');
        observer.observe(element);
    });
}

// Initialize newsletter form
function initializeNewsletterForm() {
    const form = document.getElementById('newsletterForm');
    if (!form) return;
    
    form.addEventListener('submit', handleNewsletterSubmit);
}

// Handle newsletter form submission
async function handleNewsletterSubmit(e) {
    e.preventDefault();
    
    const form = e.target;
    const emailInput = form.querySelector('.email-input');
    const submitButton = form.querySelector('.btn');
    const email = emailInput.value.trim();
    
    // Validate email
    if (!isValidEmail(email)) {
        showNewsletterMessage('Please enter a valid email address.', 'error');
        return;
    }
    
    // Show loading state
    const originalButtonText = submitButton.innerHTML;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Subscribing...';
    submitButton.disabled = true;
    
    try {
        // Simulate newsletter subscription
        await simulateNewsletterSubscription(email);
        
        // Show success message
        showNewsletterMessage('Thank you for subscribing! Check your email for confirmation.', 'success');
        
        // Reset form
        form.reset();
        
        // Track subscription
        trackNewsletterSubscription(email);
        
    } catch (error) {
        console.error('Newsletter subscription error:', error);
        showNewsletterMessage('Sorry, there was an error. Please try again later.', 'error');
    } finally {
        // Reset button
        submitButton.innerHTML = originalButtonText;
        submitButton.disabled = false;
    }
}

// Simulate newsletter subscription
function simulateNewsletterSubscription(email) {
    return new Promise((resolve, reject) => {
        // Store subscription in localStorage
        const subscriptions = Utils.storage.get('newsletter_subscriptions', []);
        
        // Check if already subscribed
        if (subscriptions.includes(email)) {
            reject(new Error('Email already subscribed'));
            return;
        }
        
        subscriptions.push(email);
        Utils.storage.set('newsletter_subscriptions', subscriptions);
        
        // Simulate network delay
        setTimeout(() => {
            // Simulate 95% success rate
            if (Math.random() > 0.05) {
                resolve({ email, subscribed: true });
            } else {
                reject(new Error('Network error'));
            }
        }, 1500);
    });
}

// Show newsletter message
function showNewsletterMessage(message, type) {
    // Remove existing message
    const existingMessage = document.querySelector('.newsletter-message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    // Create message element
    const messageElement = document.createElement('div');
    messageElement.className = `newsletter-message ${type}`;
    messageElement.textContent = message;
    
    // Insert after form
    const form = document.getElementById('newsletterForm');
    form.parentNode.insertBefore(messageElement, form.nextSibling);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (messageElement.parentNode) {
            messageElement.remove();
        }
    }, 5000);
}

// Validate email format
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Track newsletter subscription
function trackNewsletterSubscription(email) {
    const analytics = Utils.storage.get('newsletter_analytics', {
        subscriptions: 0,
        emails: []
    });
    
    analytics.subscriptions++;
    analytics.emails.push({
        email: email,
        timestamp: new Date().toISOString()
    });
    
    Utils.storage.set('newsletter_analytics', analytics);
    
    // Track with external analytics if available
    if (typeof gtag !== 'undefined') {
        gtag('event', 'newsletter_signup', {
            event_category: 'Newsletter',
            event_label: 'Blog Page'
        });
    }
}

// Initialize load more functionality
function initializeLoadMore() {
    const loadMoreBtn = document.querySelector('.load-more-btn');
    if (!loadMoreBtn) return;
    
    loadMoreBtn.addEventListener('click', handleLoadMore);
}

// Handle load more articles
async function handleLoadMore() {
    const loadMoreBtn = document.querySelector('.load-more-btn');
    const articlesList = document.querySelector('.articles-list');
    
    // Show loading state
    const originalText = loadMoreBtn.innerHTML;
    loadMoreBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
    loadMoreBtn.disabled = true;
    
    try {
        // Simulate loading more articles
        const newArticles = await loadMoreArticles();
        
        // Add new articles to the list
        newArticles.forEach(article => {
            const articleElement = createArticleElement(article);
            articlesList.appendChild(articleElement);
            
            // Animate in
            setTimeout(() => {
                articleElement.classList.add('animate-in');
            }, 100);
        });
        
        // Update button text
        loadMoreBtn.innerHTML = '<i class="fas fa-plus"></i> Load More Articles';
        
    } catch (error) {
        console.error('Load more error:', error);
        loadMoreBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Error Loading';
        
        setTimeout(() => {
            loadMoreBtn.innerHTML = originalText;
        }, 3000);
    } finally {
        loadMoreBtn.disabled = false;
    }
}

// Simulate loading more articles
function loadMoreArticles() {
    return new Promise((resolve) => {
        setTimeout(() => {
            const articles = [
                {
                    title: "The ENFP Personality: Understanding the Campaigner",
                    excerpt: "Dive deep into the enthusiastic and creative world of ENFPs and their unique approach to life and relationships.",
                    category: "Personality Types",
                    date: "July 27, 2025",
                    readTime: "6 min read",
                    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face"
                },
                {
                    title: "Mindfulness Techniques for Different Personality Types",
                    excerpt: "Discover personalized mindfulness and meditation practices tailored to your specific personality type.",
                    category: "Mental Health",
                    date: "July 26, 2025",
                    readTime: "9 min read",
                    image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=150&h=150&fit=crop"
                }
            ];
            resolve(articles);
        }, 1000);
    });
}

// Create article element
function createArticleElement(article) {
    const articleElement = document.createElement('article');
    articleElement.className = 'article-item animate-on-scroll';
    articleElement.innerHTML = `
        <div class="article-thumbnail">
            <img src="${article.image}" alt="${article.title}">
        </div>
        <div class="article-info">
            <div class="article-category-tag">${article.category}</div>
            <h3 class="article-title">
                <a href="#">${article.title}</a>
            </h3>
            <p class="article-excerpt">${article.excerpt}</p>
            <div class="article-meta">
                <span class="article-date">${article.date}</span>
                <span class="article-read-time">${article.readTime}</span>
            </div>
        </div>
    `;
    
    return articleElement;
}

// Initialize category cards
function initializeCategoryCards() {
    const categoryCards = document.querySelectorAll('.category-card');
    
    categoryCards.forEach(card => {
        card.addEventListener('click', function() {
            const categoryTitle = this.querySelector('.category-title').textContent;
            handleCategoryClick(categoryTitle);
        });
        
        // Add hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// Handle category click
function handleCategoryClick(categoryTitle) {
    // In a real application, this would filter articles or navigate to a category page
    console.log(`Clicked on category: ${categoryTitle}`);
    
    // For demo purposes, show a message
    showCategoryMessage(`Showing articles in "${categoryTitle}" category`);
    
    // Track category click
    if (typeof gtag !== 'undefined') {
        gtag('event', 'category_click', {
            event_category: 'Blog',
            event_label: categoryTitle
        });
    }
}

// Show category message
function showCategoryMessage(message) {
    // Create temporary message
    const messageElement = document.createElement('div');
    messageElement.className = 'category-message';
    messageElement.textContent = message;
    messageElement.style.cssText = `
        position: fixed;
        top: 100px;
        left: 50%;
        transform: translateX(-50%);
        background: var(--primary-color);
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        z-index: 1000;
        font-weight: 500;
        box-shadow: var(--shadow-lg);
    `;
    
    document.body.appendChild(messageElement);
    
    // Remove after 3 seconds
    setTimeout(() => {
        messageElement.remove();
    }, 3000);
}

// Initialize article interactions
function initializeArticleInteractions() {
    const articles = document.querySelectorAll('.featured-article, .article-item');
    
    articles.forEach(article => {
        const link = article.querySelector('.article-title a');
        if (link) {
            // Make entire article clickable
            article.addEventListener('click', function(e) {
                if (e.target.tagName !== 'A') {
                    link.click();
                }
            });
            
            // Track article clicks
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const articleTitle = this.textContent;
                trackArticleClick(articleTitle);
                
                // In a real application, this would navigate to the article
                console.log(`Clicked on article: ${articleTitle}`);
                showArticleMessage(`Opening "${articleTitle}"`);
            });
        }
    });
}

// Track article click
function trackArticleClick(articleTitle) {
    const analytics = Utils.storage.get('article_analytics', {
        clicks: 0,
        articles: {}
    });
    
    analytics.clicks++;
    analytics.articles[articleTitle] = (analytics.articles[articleTitle] || 0) + 1;
    
    Utils.storage.set('article_analytics', analytics);
    
    if (typeof gtag !== 'undefined') {
        gtag('event', 'article_click', {
            event_category: 'Blog',
            event_label: articleTitle
        });
    }
}

// Show article message
function showArticleMessage(message) {
    const messageElement = document.createElement('div');
    messageElement.className = 'article-message';
    messageElement.textContent = message;
    messageElement.style.cssText = `
        position: fixed;
        top: 100px;
        left: 50%;
        transform: translateX(-50%);
        background: var(--accent-color);
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        z-index: 1000;
        font-weight: 500;
        box-shadow: var(--shadow-lg);
    `;
    
    document.body.appendChild(messageElement);
    
    setTimeout(() => {
        messageElement.remove();
    }, 2000);
}

// Search functionality (if search input exists)
function initializeSearch() {
    const searchInput = document.querySelector('.blog-search-input');
    if (!searchInput) return;
    
    searchInput.addEventListener('input', Utils.debounce(handleSearch, 300));
}

// Handle search
function handleSearch(e) {
    const query = e.target.value.trim().toLowerCase();
    const articles = document.querySelectorAll('.featured-article, .article-item');
    
    if (query.length === 0) {
        // Show all articles
        articles.forEach(article => {
            article.style.display = '';
        });
        return;
    }
    
    // Filter articles
    articles.forEach(article => {
        const title = article.querySelector('.article-title a').textContent.toLowerCase();
        const excerpt = article.querySelector('.article-excerpt').textContent.toLowerCase();
        const category = article.querySelector('.article-category, .article-category-tag').textContent.toLowerCase();
        
        const matches = title.includes(query) || excerpt.includes(query) || category.includes(query);
        article.style.display = matches ? '' : 'none';
    });
}

// Initialize search on load
document.addEventListener('DOMContentLoaded', initializeSearch);

// Add CSS for messages and animations
const blogStyles = `
<style>
.newsletter-message {
    padding: var(--spacing-4);
    border-radius: var(--radius-lg);
    margin-top: var(--spacing-4);
    font-weight: 500;
    text-align: center;
}

.newsletter-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.newsletter-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.category-message,
.article-message {
    animation: slideInDown 0.3s ease, slideOutUp 0.3s ease 2.7s;
}

@keyframes slideInDown {
    from {
        transform: translateX(-50%) translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(-50%) translateY(0);
        opacity: 1;
    }
}

@keyframes slideOutUp {
    from {
        transform: translateX(-50%) translateY(0);
        opacity: 1;
    }
    to {
        transform: translateX(-50%) translateY(-20px);
        opacity: 0;
    }
}

/* Enhanced hover effects */
.featured-article:hover {
    cursor: pointer;
}

.article-item:hover {
    cursor: pointer;
}

.category-card:hover {
    cursor: pointer;
}

/* Loading state for load more button */
.load-more-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Responsive newsletter form */
@media (max-width: 768px) {
    .newsletter-content {
        text-align: center;
    }
    
    .signup-form .form-group {
        flex-direction: column;
        gap: var(--spacing-3);
    }
    
    .email-input {
        width: 100%;
    }
    
    .signup-form .btn {
        width: 100%;
        justify-content: center;
    }
}
</style>
`;

// Add styles to head
document.head.insertAdjacentHTML('beforeend', blogStyles);
