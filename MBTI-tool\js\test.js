// Test page functionality

// Test state management
let testState = {
    currentQuestion: 0,
    answers: [],
    startTime: null,
    endTime: null,
    personalityResult: null
};

// Comprehensive question database
const questions = [
    // Extraversion vs Introversion questions
    {
        id: 1,
        text: "At a party, you're more likely to:",
        options: [
            { text: "Interact with many people, including strangers", dimension: "E", weight: 2 },
            { text: "Interact with a few people you know well", dimension: "I", weight: 2 }
        ]
    },
    {
        id: 2,
        text: "In your free time, you're more likely to:",
        options: [
            { text: "Go out and be around other people", dimension: "E", weight: 2 },
            { text: "Stay home and pursue solitary activities", dimension: "I", weight: 2 }
        ]
    },
    {
        id: 3,
        text: "When you need to recharge, you:",
        options: [
            { text: "Spend time with friends or in social settings", dimension: "E", weight: 2 },
            { text: "Spend time alone or in quiet environments", dimension: "I", weight: 2 }
        ]
    },
    {
        id: 4,
        text: "In group discussions, you tend to:",
        options: [
            { text: "Think out loud and share ideas as they come", dimension: "E", weight: 2 },
            { text: "Think things through before speaking", dimension: "I", weight: 2 }
        ]
    },
    {
        id: 5,
        text: "You feel more comfortable:",
        options: [
            { text: "Being the center of attention", dimension: "E", weight: 1 },
            { text: "Staying in the background", dimension: "I", weight: 1 }
        ]
    },

    // Sensing vs Intuition questions
    {
        id: 6,
        text: "When learning something new, you prefer:",
        options: [
            { text: "Step-by-step instructions and concrete examples", dimension: "S", weight: 2 },
            { text: "Understanding the big picture and underlying concepts", dimension: "N", weight: 2 }
        ]
    },
    {
        id: 7,
        text: "You're more interested in:",
        options: [
            { text: "What is actually happening right now", dimension: "S", weight: 2 },
            { text: "What could happen in the future", dimension: "N", weight: 2 }
        ]
    },
    {
        id: 8,
        text: "When reading, you prefer:",
        options: [
            { text: "Practical, factual information", dimension: "S", weight: 2 },
            { text: "Theoretical concepts and possibilities", dimension: "N", weight: 2 }
        ]
    },
    {
        id: 9,
        text: "You trust:",
        options: [
            { text: "Your experience and proven methods", dimension: "S", weight: 2 },
            { text: "Your intuition and new possibilities", dimension: "N", weight: 2 }
        ]
    },
    {
        id: 10,
        text: "You're more likely to notice:",
        options: [
            { text: "Details and specific facts", dimension: "S", weight: 1 },
            { text: "Patterns and connections", dimension: "N", weight: 1 }
        ]
    },

    // Thinking vs Feeling questions
    {
        id: 11,
        text: "When making important decisions, you:",
        options: [
            { text: "Focus on logic and objective analysis", dimension: "T", weight: 2 },
            { text: "Consider how it affects people and personal values", dimension: "F", weight: 2 }
        ]
    },
    {
        id: 12,
        text: "You value:",
        options: [
            { text: "Fairness and justice", dimension: "T", weight: 2 },
            { text: "Harmony and compassion", dimension: "F", weight: 2 }
        ]
    },
    {
        id: 13,
        text: "When giving feedback, you tend to:",
        options: [
            { text: "Be direct and focus on facts", dimension: "T", weight: 2 },
            { text: "Be tactful and consider feelings", dimension: "F", weight: 2 }
        ]
    },
    {
        id: 14,
        text: "You're more motivated by:",
        options: [
            { text: "Achievement and competence", dimension: "T", weight: 2 },
            { text: "Appreciation and personal connection", dimension: "F", weight: 2 }
        ]
    },
    {
        id: 15,
        text: "In conflicts, you prefer to:",
        options: [
            { text: "Address the issue directly and logically", dimension: "T", weight: 1 },
            { text: "Consider everyone's feelings and find compromise", dimension: "F", weight: 1 }
        ]
    },

    // Judging vs Perceiving questions
    {
        id: 16,
        text: "You prefer to:",
        options: [
            { text: "Have things settled and decided", dimension: "J", weight: 2 },
            { text: "Keep your options open and flexible", dimension: "P", weight: 2 }
        ]
    },
    {
        id: 17,
        text: "Your workspace is typically:",
        options: [
            { text: "Organized and structured", dimension: "J", weight: 2 },
            { text: "Flexible and adaptable", dimension: "P", weight: 2 }
        ]
    },
    {
        id: 18,
        text: "When planning a vacation, you:",
        options: [
            { text: "Plan activities and make reservations in advance", dimension: "J", weight: 2 },
            { text: "Keep plans loose and decide as you go", dimension: "P", weight: 2 }
        ]
    },
    {
        id: 19,
        text: "You work best with:",
        options: [
            { text: "Clear deadlines and structured timelines", dimension: "J", weight: 2 },
            { text: "Flexible schedules and open-ended projects", dimension: "P", weight: 2 }
        ]
    },
    {
        id: 20,
        text: "You prefer:",
        options: [
            { text: "Making decisions quickly and moving forward", dimension: "J", weight: 1 },
            { text: "Gathering more information before deciding", dimension: "P", weight: 1 }
        ]
    }
];

// Comprehensive personality type data
const personalityTypes = {
    // Analysts (NT)
    INTJ: {
        name: "The Architect",
        description: "Imaginative and strategic thinkers, with a plan for everything.",
        traits: [
            { icon: "fas fa-lightbulb", title: "Strategic Thinking", description: "You excel at seeing the big picture and developing long-term plans." },
            { icon: "fas fa-target", title: "Goal-Oriented", description: "You're driven to achieve your objectives and turn ideas into reality." },
            { icon: "fas fa-user-tie", title: "Independent", description: "You prefer to work autonomously and trust your own judgment." },
            { icon: "fas fa-chart-line", title: "Analytical", description: "You approach problems with logic and systematic thinking." }
        ]
    },
    INTP: {
        name: "The Thinker",
        description: "Innovative inventors with an unquenchable thirst for knowledge.",
        traits: [
            { icon: "fas fa-microscope", title: "Logical", description: "You approach problems with systematic and objective thinking." },
            { icon: "fas fa-puzzle-piece", title: "Creative", description: "You enjoy exploring new ideas and theoretical concepts." },
            { icon: "fas fa-search", title: "Curious", description: "You have an insatiable desire to understand how things work." },
            { icon: "fas fa-balance-scale", title: "Objective", description: "You value truth and accuracy above personal considerations." }
        ]
    },
    ENTJ: {
        name: "The Commander",
        description: "Bold, imaginative and strong-willed leaders, always finding a way or making one.",
        traits: [
            { icon: "fas fa-crown", title: "Natural Leader", description: "You naturally take charge and organize people toward common goals." },
            { icon: "fas fa-bolt", title: "Efficient", description: "You focus on getting things done quickly and effectively." },
            { icon: "fas fa-mountain", title: "Ambitious", description: "You set high goals and work tirelessly to achieve them." },
            { icon: "fas fa-chess", title: "Strategic", description: "You think several steps ahead and plan for long-term success." }
        ]
    },
    ENTP: {
        name: "The Debater",
        description: "Smart and curious thinkers who cannot resist an intellectual challenge.",
        traits: [
            { icon: "fas fa-comments", title: "Quick-witted", description: "You think fast and enjoy intellectual debates and discussions." },
            { icon: "fas fa-lightbulb", title: "Innovative", description: "You generate creative solutions and see new possibilities." },
            { icon: "fas fa-fire", title: "Energetic", description: "You bring enthusiasm and energy to projects and ideas." },
            { icon: "fas fa-network-wired", title: "Versatile", description: "You adapt quickly and excel in diverse situations." }
        ]
    },

    // Diplomats (NF)
    INFJ: {
        name: "The Advocate",
        description: "Quiet and mystical, yet very inspiring and tireless idealists.",
        traits: [
            { icon: "fas fa-eye", title: "Insightful", description: "You have deep insights into people and situations." },
            { icon: "fas fa-heart", title: "Empathetic", description: "You understand and share the feelings of others deeply." },
            { icon: "fas fa-star", title: "Idealistic", description: "You're driven by your values and vision for a better world." },
            { icon: "fas fa-shield-alt", title: "Determined", description: "You're committed to your beliefs and follow through on your goals." }
        ]
    },
    INFP: {
        name: "The Mediator",
        description: "Poetic, kind and altruistic people, always eager to help a good cause.",
        traits: [
            { icon: "fas fa-dove", title: "Idealistic", description: "You're guided by your values and desire to make the world better." },
            { icon: "fas fa-palette", title: "Creative", description: "You express yourself through art, writing, or other creative outlets." },
            { icon: "fas fa-hands-helping", title: "Compassionate", description: "You care deeply about others and want to help them." },
            { icon: "fas fa-leaf", title: "Authentic", description: "You stay true to yourself and your personal values." }
        ]
    },
    ENFJ: {
        name: "The Protagonist",
        description: "Charismatic and inspiring leaders, able to mesmerize their listeners.",
        traits: [
            { icon: "fas fa-users", title: "Charismatic", description: "You naturally inspire and motivate others to reach their potential." },
            { icon: "fas fa-handshake", title: "Diplomatic", description: "You're skilled at bringing people together and resolving conflicts." },
            { icon: "fas fa-seedling", title: "Growth-Oriented", description: "You help others develop and achieve their goals." },
            { icon: "fas fa-compass", title: "Values-Driven", description: "You're guided by strong personal values and principles." }
        ]
    },
    ENFP: {
        name: "The Campaigner",
        description: "Enthusiastic, creative and sociable free spirits, who can always find a reason to smile.",
        traits: [
            { icon: "fas fa-heart", title: "Enthusiastic", description: "You bring energy and excitement to everything you do." },
            { icon: "fas fa-palette", title: "Creative", description: "You see possibilities and connections others might miss." },
            { icon: "fas fa-users", title: "People-Focused", description: "You genuinely care about others and their well-being." },
            { icon: "fas fa-rocket", title: "Inspiring", description: "You motivate others to pursue their dreams and potential." }
        ]
    },

    // Sentinels (SJ)
    ISTJ: {
        name: "The Logistician",
        description: "Practical and fact-minded, reliable and responsible.",
        traits: [
            { icon: "fas fa-shield-alt", title: "Reliable", description: "Others can count on you to follow through on commitments." },
            { icon: "fas fa-clipboard-check", title: "Organized", description: "You prefer structure and systematic approaches to tasks." },
            { icon: "fas fa-handshake", title: "Dutiful", description: "You take your responsibilities seriously and honor your obligations." },
            { icon: "fas fa-chart-bar", title: "Practical", description: "You focus on realistic, proven solutions to problems." }
        ]
    },
    ISFJ: {
        name: "The Protector",
        description: "Very dedicated and warm protectors, always ready to defend their loved ones.",
        traits: [
            { icon: "fas fa-heart", title: "Caring", description: "You're deeply concerned about others' well-being and happiness." },
            { icon: "fas fa-hands-helping", title: "Supportive", description: "You provide practical help and emotional support to others." },
            { icon: "fas fa-user-friends", title: "Loyal", description: "You're committed to your relationships and stand by your loved ones." },
            { icon: "fas fa-clock", title: "Patient", description: "You're willing to work steadily toward long-term goals." }
        ]
    },
    ESTJ: {
        name: "The Executive",
        description: "Excellent administrators, unsurpassed at managing things or people.",
        traits: [
            { icon: "fas fa-briefcase", title: "Organized", description: "You excel at creating structure and managing complex projects." },
            { icon: "fas fa-gavel", title: "Decisive", description: "You make decisions quickly and stick to them." },
            { icon: "fas fa-users-cog", title: "Leadership", description: "You naturally take charge and guide others toward goals." },
            { icon: "fas fa-trophy", title: "Results-Oriented", description: "You focus on achieving concrete, measurable outcomes." }
        ]
    },
    ESFJ: {
        name: "The Consul",
        description: "Extraordinarily caring, social and popular people, always eager to help.",
        traits: [
            { icon: "fas fa-heart", title: "Warm-hearted", description: "You genuinely care about others and show it through your actions." },
            { icon: "fas fa-users", title: "Social", description: "You enjoy being around people and building relationships." },
            { icon: "fas fa-hands-helping", title: "Helpful", description: "You're always ready to lend a hand and support others." },
            { icon: "fas fa-balance-scale", title: "Harmonious", description: "You work to maintain peace and cooperation in groups." }
        ]
    },

    // Explorers (SP)
    ISTP: {
        name: "The Virtuoso",
        description: "Bold and practical experimenters, masters of all kinds of tools.",
        traits: [
            { icon: "fas fa-tools", title: "Practical", description: "You excel at working with your hands and solving real-world problems." },
            { icon: "fas fa-leaf", title: "Adaptable", description: "You're flexible and can adjust quickly to changing situations." },
            { icon: "fas fa-search", title: "Curious", description: "You enjoy exploring how things work and experimenting with new approaches." },
            { icon: "fas fa-mountain", title: "Independent", description: "You prefer to work alone and make your own decisions." }
        ]
    },
    ISFP: {
        name: "The Adventurer",
        description: "Flexible and charming artists, always ready to explore new possibilities.",
        traits: [
            { icon: "fas fa-palette", title: "Artistic", description: "You have a natural appreciation for beauty and creative expression." },
            { icon: "fas fa-heart", title: "Sensitive", description: "You're attuned to emotions and care deeply about others' feelings." },
            { icon: "fas fa-dove", title: "Gentle", description: "You approach others with kindness and avoid conflict when possible." },
            { icon: "fas fa-compass", title: "Values-Driven", description: "You make decisions based on your personal values and beliefs." }
        ]
    },
    ESTP: {
        name: "The Entrepreneur",
        description: "Smart, energetic and very perceptive people, truly enjoy living on the edge.",
        traits: [
            { icon: "fas fa-bolt", title: "Energetic", description: "You bring high energy and enthusiasm to everything you do." },
            { icon: "fas fa-eye", title: "Observant", description: "You notice details and changes in your environment quickly." },
            { icon: "fas fa-handshake", title: "Social", description: "You enjoy being around people and are naturally charismatic." },
            { icon: "fas fa-rocket", title: "Action-Oriented", description: "You prefer to learn by doing and jump into new experiences." }
        ]
    },
    ESFP: {
        name: "The Entertainer",
        description: "Spontaneous, energetic and enthusiastic people – life is never boring around them.",
        traits: [
            { icon: "fas fa-theater-masks", title: "Entertaining", description: "You naturally bring joy and fun to social situations." },
            { icon: "fas fa-sun", title: "Optimistic", description: "You maintain a positive outlook and see the best in people." },
            { icon: "fas fa-heart", title: "Warm", description: "You're genuinely interested in others and make them feel valued." },
            { icon: "fas fa-magic", title: "Spontaneous", description: "You embrace new experiences and live in the moment." }
        ]
    }
};

document.addEventListener('DOMContentLoaded', function() {
    initializeTest();
    setupEventListeners();
});

function initializeTest() {
    // Show welcome screen by default
    showScreen('welcomeScreen');
    
    // Initialize test data
    testState.currentQuestion = 0;
    testState.answers = [];
    testState.startTime = null;
    testState.endTime = null;
    
    // Update total questions display
    document.getElementById('totalQuestions').textContent = questions.length;
}

function setupEventListeners() {
    // Start test button
    document.getElementById('startTestBtn').addEventListener('click', startTest);
    
    // Navigation buttons
    document.getElementById('prevBtn').addEventListener('click', previousQuestion);
    document.getElementById('nextBtn').addEventListener('click', nextQuestion);
    
    // Retake test button
    document.getElementById('retakeTestBtn').addEventListener('click', retakeTest);
    
    // Share buttons
    document.getElementById('shareImageBtn').addEventListener('click', generateShareImage);
    document.getElementById('shareLinkBtn').addEventListener('click', generateShareLink);
    document.getElementById('downloadPdfBtn').addEventListener('click', downloadPdfReport);
    
    // Social share buttons
    document.getElementById('shareTwitter').addEventListener('click', () => shareOnSocial('twitter'));
    document.getElementById('shareFacebook').addEventListener('click', () => shareOnSocial('facebook'));
    document.getElementById('shareLinkedIn').addEventListener('click', () => shareOnSocial('linkedin'));
    document.getElementById('shareWhatsApp').addEventListener('click', () => shareOnSocial('whatsapp'));
    document.getElementById('shareInstagram').addEventListener('click', () => shareOnSocial('instagram'));
    document.getElementById('shareTikTok').addEventListener('click', () => shareOnSocial('tiktok'));

    // Inspiration tag clicks
    document.querySelectorAll('.inspiration-tag').forEach(tag => {
        tag.addEventListener('click', function() {
            const text = this.textContent.replace('[TYPE]', testState.personalityResult?.type || 'INTJ');
            copyToClipboard(text);
            showShareFeedback('Share text copied! 📋');
        });
    });
    
    // Modal controls
    document.getElementById('closeShareModal').addEventListener('click', closeShareModal);
    document.getElementById('downloadImageBtn').addEventListener('click', downloadShareImage);
    document.getElementById('copyImageBtn').addEventListener('click', copyImageToClipboard);
    
    // Close modal when clicking overlay
    document.getElementById('shareImageModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeShareModal();
        }
    });
}

function showScreen(screenId) {
    // Hide all screens
    document.querySelectorAll('.test-container section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Show target screen
    document.getElementById(screenId).classList.add('active');
}

function startTest() {
    testState.startTime = new Date();
    testState.currentQuestion = 0;
    testState.answers = [];
    
    showScreen('questionScreen');
    displayQuestion();
}

function displayQuestion() {
    const question = questions[testState.currentQuestion];
    if (!question) return;

    // Add transition effect
    const questionCard = document.querySelector('.question-card');
    questionCard.classList.add('transitioning-out');

    setTimeout(() => {
        // Update progress
        updateProgress();

        // Display question text with typewriter effect
        const questionTextElement = document.getElementById('questionText');
        questionTextElement.textContent = '';
        typeWriterEffect(questionTextElement, question.text, 30);

        // Create answer options with staggered animation
        const optionsContainer = document.getElementById('answerOptions');
        optionsContainer.innerHTML = '';

        question.options.forEach((option, index) => {
            const optionElement = document.createElement('div');
            optionElement.className = 'answer-option';
            optionElement.innerHTML = `<p class="option-text">${option.text}</p>`;

            // Add click handler with haptic feedback
            optionElement.addEventListener('click', () => {
                selectAnswer(index);
                // Add haptic feedback for mobile devices
                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }
            });

            // Add hover sound effect (optional)
            optionElement.addEventListener('mouseenter', () => {
                optionElement.style.transform = 'translateY(-4px) scale(1.02)';
            });

            optionElement.addEventListener('mouseleave', () => {
                if (!optionElement.classList.contains('selected')) {
                    optionElement.style.transform = 'translateY(0) scale(1)';
                }
            });

            optionsContainer.appendChild(optionElement);
        });

        // Update navigation buttons
        updateNavigationButtons();

        // Restore previous answer if exists
        const previousAnswer = testState.answers[testState.currentQuestion];
        if (previousAnswer !== undefined) {
            selectAnswer(previousAnswer, false);
        }

        // Remove transition class and add entrance animation
        questionCard.classList.remove('transitioning-out');
        questionCard.classList.add('transitioning-in');

        setTimeout(() => {
            questionCard.classList.remove('transitioning-in');
        }, 300);

    }, 150);
}

function typeWriterEffect(element, text, speed = 50) {
    let i = 0;
    const timer = setInterval(() => {
        if (i < text.length) {
            element.textContent += text.charAt(i);
            i++;
        } else {
            clearInterval(timer);
        }
    }, speed);
}

function selectAnswer(optionIndex, recordAnswer = true) {
    // Remove previous selection with animation
    document.querySelectorAll('.answer-option').forEach((option, index) => {
        option.classList.remove('selected');
        if (index !== optionIndex) {
            option.style.transform = 'translateY(0) scale(1)';
            option.style.opacity = '0.7';
        }
    });

    // Select new option with enhanced animation
    const selectedOption = document.querySelectorAll('.answer-option')[optionIndex];
    selectedOption.classList.add('selected');
    selectedOption.style.opacity = '1';

    // Add selection celebration effect
    createSelectionEffect(selectedOption);

    // Record answer
    if (recordAnswer) {
        testState.answers[testState.currentQuestion] = optionIndex;

        // Add progress celebration
        updateProgressWithCelebration();
    }

    // Enable next button with animation
    const nextBtn = document.getElementById('nextBtn');
    nextBtn.disabled = false;
    nextBtn.style.animation = 'buttonPulse 0.3s ease-out';

    setTimeout(() => {
        nextBtn.style.animation = '';
    }, 300);
}

function createSelectionEffect(element) {
    // Create ripple effect
    const ripple = document.createElement('div');
    ripple.style.cssText = `
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        animation: rippleEffect 0.6s ease-out;
        pointer-events: none;
        z-index: 1;
    `;

    element.style.position = 'relative';
    element.appendChild(ripple);

    setTimeout(() => ripple.remove(), 600);

    // Add sparkle effect
    for (let i = 0; i < 5; i++) {
        setTimeout(() => {
            createSparkle(element);
        }, i * 100);
    }
}

function createSparkle(parent) {
    const sparkle = document.createElement('div');
    sparkle.textContent = '✨';
    sparkle.style.cssText = `
        position: absolute;
        top: ${Math.random() * 100}%;
        left: ${Math.random() * 100}%;
        font-size: 12px;
        animation: sparkleFloat 1s ease-out forwards;
        pointer-events: none;
        z-index: 2;
    `;

    parent.appendChild(sparkle);
    setTimeout(() => sparkle.remove(), 1000);
}

function updateProgressWithCelebration() {
    const progressFill = document.getElementById('progressFill');
    const currentProgress = ((testState.currentQuestion + 1) / questions.length) * 100;

    // Add celebration effect to progress bar
    progressFill.style.animation = 'progressCelebration 0.5s ease-out';

    setTimeout(() => {
        progressFill.style.animation = '';
        progressFill.style.width = currentProgress + '%';
    }, 250);
}

function updateProgress() {
    const progress = ((testState.currentQuestion + 1) / questions.length) * 100;
    
    document.getElementById('currentQuestion').textContent = testState.currentQuestion + 1;
    document.getElementById('progressPercent').textContent = Math.round(progress);
    document.getElementById('progressFill').style.width = progress + '%';
}

function updateNavigationButtons() {
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    
    // Previous button
    prevBtn.disabled = testState.currentQuestion === 0;
    
    // Next button
    const hasAnswer = testState.answers[testState.currentQuestion] !== undefined;
    nextBtn.disabled = !hasAnswer;
    
    // Update next button text for last question
    if (testState.currentQuestion === questions.length - 1) {
        nextBtn.innerHTML = 'Finish <i class="fas fa-check"></i>';
    } else {
        nextBtn.innerHTML = 'Next <i class="fas fa-chevron-right"></i>';
    }
}

function previousQuestion() {
    if (testState.currentQuestion > 0) {
        testState.currentQuestion--;
        displayQuestion();
    }
}

function nextQuestion() {
    if (testState.currentQuestion < questions.length - 1) {
        testState.currentQuestion++;
        displayQuestion();
    } else {
        // Finish test
        finishTest();
    }
}

function finishTest() {
    testState.endTime = new Date();
    
    // Show loading screen
    showScreen('loadingScreen');
    
    // Simulate AI analysis with loading steps
    simulateAnalysis();
}

function simulateAnalysis() {
    const steps = ['step1', 'step2', 'step3', 'step4'];
    let currentStep = 0;
    
    const interval = setInterval(() => {
        // Remove active from previous step
        if (currentStep > 0) {
            document.getElementById(steps[currentStep - 1]).classList.remove('active');
        }
        
        // Add active to current step
        if (currentStep < steps.length) {
            document.getElementById(steps[currentStep]).classList.add('active');
            currentStep++;
        } else {
            clearInterval(interval);
            // Analysis complete, calculate results
            calculateResults();
        }
    }, 1000);
}

function calculateResults() {
    // Calculate personality type based on answers
    const scores = { E: 0, I: 0, S: 0, N: 0, T: 0, F: 0, J: 0, P: 0 };
    
    testState.answers.forEach((answerIndex, questionIndex) => {
        const question = questions[questionIndex];
        const selectedOption = question.options[answerIndex];
        scores[selectedOption.dimension] += selectedOption.weight;
    });
    
    // Determine personality type
    const type = 
        (scores.E > scores.I ? 'E' : 'I') +
        (scores.S > scores.N ? 'S' : 'N') +
        (scores.T > scores.F ? 'T' : 'F') +
        (scores.J > scores.P ? 'J' : 'P');
    
    // Calculate percentages for each dimension
    const dimensions = {
        energy: {
            type: scores.E > scores.I ? 'E' : 'I',
            name: scores.E > scores.I ? 'Extraversion (E)' : 'Introversion (I)',
            percentage: scores.E > scores.I ? 
                Math.round((scores.E / (scores.E + scores.I)) * 100) :
                Math.round((scores.I / (scores.E + scores.I)) * 100),
            description: scores.E > scores.I ? 
                'You prefer to focus on the outer world and get energy from interaction.' :
                'You prefer to focus on your inner world and recharge through solitude.'
        },
        information: {
            type: scores.S > scores.N ? 'S' : 'N',
            name: scores.S > scores.N ? 'Sensing (S)' : 'Intuition (N)',
            percentage: scores.S > scores.N ? 
                Math.round((scores.S / (scores.S + scores.N)) * 100) :
                Math.round((scores.N / (scores.S + scores.N)) * 100),
            description: scores.S > scores.N ? 
                'You focus on concrete facts, details, and practical information.' :
                'You focus on patterns, possibilities, and future potential.'
        },
        decision: {
            type: scores.T > scores.F ? 'T' : 'F',
            name: scores.T > scores.F ? 'Thinking (T)' : 'Feeling (F)',
            percentage: scores.T > scores.F ? 
                Math.round((scores.T / (scores.T + scores.F)) * 100) :
                Math.round((scores.F / (scores.T + scores.F)) * 100),
            description: scores.T > scores.F ? 
                'You prioritize logic and objective analysis in decision-making.' :
                'You prioritize values and consider how decisions affect people.'
        },
        lifestyle: {
            type: scores.J > scores.P ? 'J' : 'P',
            name: scores.J > scores.P ? 'Judging (J)' : 'Perceiving (P)',
            percentage: scores.J > scores.P ? 
                Math.round((scores.J / (scores.J + scores.P)) * 100) :
                Math.round((scores.P / (scores.J + scores.P)) * 100),
            description: scores.J > scores.P ? 
                'You prefer structure, planning, and closure in your approach to life.' :
                'You prefer flexibility, spontaneity, and keeping options open.'
        }
    };
    
    testState.personalityResult = {
        type: type,
        typeData: personalityTypes[type] || personalityTypes.INTJ, // Fallback
        dimensions: dimensions,
        testDuration: Math.round((testState.endTime - testState.startTime) / 1000 / 60), // minutes
        completedAt: new Date().toISOString()
    };
    
    // Store results in localStorage
    Utils.storage.set('personality_test_result', testState.personalityResult);
    
    // Display results
    displayResults();
}

function displayResults() {
    const result = testState.personalityResult;

    // Update personality type display
    document.getElementById('personalityType').textContent = result.type;
    document.getElementById('personalityName').textContent = result.typeData.name;
    document.getElementById('personalityDescription').textContent = result.typeData.description;

    // Add personality type specific styling
    const personalityBadge = document.querySelector('.personality-badge');
    personalityBadge.className = `personality-badge ${result.type}`;

    // Add type icon
    if (!personalityBadge.querySelector('.type-icon')) {
        const typeIcon = document.createElement('div');
        typeIcon.className = 'type-icon';
        personalityBadge.appendChild(typeIcon);
    }

    // Update dimensions
    updateDimensionDisplay('energy', result.dimensions.energy);
    updateDimensionDisplay('information', result.dimensions.information);
    updateDimensionDisplay('decision', result.dimensions.decision);
    updateDimensionDisplay('lifestyle', result.dimensions.lifestyle);

    // Update traits
    updateTraitsDisplay(result.typeData.traits);

    // Update test duration
    const durationElement = document.getElementById('testDuration');
    if (durationElement) {
        durationElement.textContent = `Completed in ${result.testDuration} minutes`;
    }

    // Show results screen
    showScreen('resultsScreen');

    // Animate results with enhanced effects
    animateResults();

    // Add celebration effect
    setTimeout(() => {
        createCelebrationEffect();
    }, 1000);

    // Add social proof animation
    setTimeout(() => {
        animateSocialProof();
    }, 1500);
}

function updateDimensionDisplay(dimensionKey, dimensionData) {
    document.getElementById(`${dimensionKey}Result`).textContent = dimensionData.name;
    document.getElementById(`${dimensionKey}Description`).textContent = dimensionData.description;
    
    // Animate progress bar
    setTimeout(() => {
        document.getElementById(`${dimensionKey}Bar`).style.width = dimensionData.percentage + '%';
    }, 500);
}

function updateTraitsDisplay(traits) {
    const traitsGrid = document.getElementById('traitsGrid');
    traitsGrid.innerHTML = '';
    
    traits.forEach(trait => {
        const traitElement = document.createElement('div');
        traitElement.className = 'trait-item';
        traitElement.innerHTML = `
            <i class="${trait.icon}"></i>
            <h3>${trait.title}</h3>
            <p>${trait.description}</p>
        `;
        traitsGrid.appendChild(traitElement);
    });
}

function animateResults() {
    // Animate personality badge first
    const badge = document.querySelector('.personality-badge');
    badge.style.animation = 'badgeEntrance 1s ease-out';

    // Animate dimensions with enhanced effects
    const dimensionItems = document.querySelectorAll('.dimension-item');
    dimensionItems.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateX(-50px) scale(0.9)';

        setTimeout(() => {
            element.style.transition = 'all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
            element.style.opacity = '1';
            element.style.transform = 'translateX(0) scale(1)';
        }, index * 150);
    });

    // Animate traits with bounce effect
    const traitItems = document.querySelectorAll('.trait-item');
    traitItems.forEach((element, index) => {
        element.style.setProperty('--i', index + 1);
        element.style.opacity = '0';
        element.style.transform = 'translateY(40px) scale(0.8)';

        setTimeout(() => {
            element.style.transition = 'all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0) scale(1)';
        }, 800 + index * 100);
    });

    // Animate share section
    setTimeout(() => {
        const shareSection = document.querySelector('.share-section');
        shareSection.style.opacity = '0';
        shareSection.style.transform = 'translateY(30px)';
        shareSection.style.transition = 'all 0.8s ease-out';

        setTimeout(() => {
            shareSection.style.opacity = '1';
            shareSection.style.transform = 'translateY(0)';
        }, 100);
    }, 1200);
}

function retakeTest() {
    // Reset test state
    testState = {
        currentQuestion: 0,
        answers: [],
        startTime: null,
        endTime: null,
        personalityResult: null
    };
    
    // Show welcome screen
    showScreen('welcomeScreen');
}

// Enhanced share functionality with personality-specific designs
function generateShareImage() {
    const canvas = document.getElementById('shareCanvas');
    const ctx = canvas.getContext('2d');
    const result = testState.personalityResult;

    // Set canvas size
    canvas.width = 800;
    canvas.height = 600;

    // Get personality-specific colors
    const typeColors = getPersonalityColors(result.type);

    // Create personality-specific gradient background
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
    gradient.addColorStop(0, typeColors.primary);
    gradient.addColorStop(0.5, typeColors.secondary);
    gradient.addColorStop(1, typeColors.accent);
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Add geometric pattern overlay
    ctx.fillStyle = 'rgba(255, 255, 255, 0.08)';
    drawGeometricPattern(ctx, canvas.width, canvas.height, result.type);

    // Add personality icon/emoji
    ctx.font = '80px Arial, sans-serif';
    ctx.textAlign = 'center';
    const emoji = getPersonalityEmoji(result.type);
    ctx.fillText(emoji, canvas.width / 2, 120);

    // Add personality type with enhanced styling
    ctx.fillStyle = 'white';
    ctx.font = 'bold 84px Inter, sans-serif';
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
    ctx.lineWidth = 3;
    ctx.strokeText(result.type, canvas.width / 2, 220);
    ctx.fillText(result.type, canvas.width / 2, 220);

    // Add type name with shadow
    ctx.font = '36px Inter, sans-serif';
    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    ctx.fillText(result.typeData.name, canvas.width / 2 + 2, 272);
    ctx.fillStyle = 'white';
    ctx.fillText(result.typeData.name, canvas.width / 2, 270);

    // Add description with better formatting
    ctx.font = '22px Inter, sans-serif';
    ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';

    // Enhanced word wrap with better spacing
    const words = result.typeData.description.split(' ');
    let line = '';
    let y = 340;
    const maxWidth = 650;
    const lineHeight = 32;

    for (let n = 0; n < words.length; n++) {
        const testLine = line + words[n] + ' ';
        const metrics = ctx.measureText(testLine);

        if (metrics.width > maxWidth && n > 0) {
            ctx.fillText(line.trim(), canvas.width / 2, y);
            line = words[n] + ' ';
            y += lineHeight;
        } else {
            line = testLine;
        }
    }
    ctx.fillText(line.trim(), canvas.width / 2, y);

    // Add key traits
    y += 60;
    ctx.font = 'bold 18px Inter, sans-serif';
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.fillText('Key Traits:', canvas.width / 2, y);

    y += 25;
    ctx.font = '16px Inter, sans-serif';
    const traits = result.typeData.traits.slice(0, 3).map(t => t.title).join(' • ');
    ctx.fillText(traits, canvas.width / 2, y);

    // Add decorative elements
    drawDecorativeElements(ctx, canvas.width, canvas.height, typeColors);

    // Add enhanced branding
    ctx.font = 'bold 18px Inter, sans-serif';
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.fillText('🧠 PersonalityPro.com', canvas.width / 2, canvas.height - 30);

    // Show modal with animation
    const modal = document.getElementById('shareImageModal');
    modal.classList.add('active');

    // Add share success animation
    setTimeout(() => {
        createShareSuccessEffect();
    }, 300);
}

function getPersonalityColors(type) {
    const colorMap = {
        'INTJ': { primary: '#4c1d95', secondary: '#7c3aed', accent: '#a855f7' },
        'INTP': { primary: '#1e40af', secondary: '#3b82f6', accent: '#60a5fa' },
        'ENTJ': { primary: '#dc2626', secondary: '#ef4444', accent: '#f87171' },
        'ENTP': { primary: '#ea580c', secondary: '#f97316', accent: '#fb923c' },
        'INFJ': { primary: '#059669', secondary: '#10b981', accent: '#34d399' },
        'INFP': { primary: '#7c2d12', secondary: '#ea580c', accent: '#f97316' },
        'ENFJ': { primary: '#be185d', secondary: '#ec4899', accent: '#f472b6' },
        'ENFP': { primary: '#c2410c', secondary: '#f97316', accent: '#fb923c' },
        'ISTJ': { primary: '#374151', secondary: '#6b7280', accent: '#9ca3af' },
        'ISFJ': { primary: '#0f766e', secondary: '#14b8a6', accent: '#5eead4' },
        'ESTJ': { primary: '#991b1b', secondary: '#dc2626', accent: '#f87171' },
        'ESFJ': { primary: '#be123c', secondary: '#e11d48', accent: '#fb7185' },
        'ISTP': { primary: '#365314', secondary: '#65a30d', accent: '#a3e635' },
        'ISFP': { primary: '#a21caf', secondary: '#c026d3', accent: '#e879f9' },
        'ESTP': { primary: '#b45309', secondary: '#d97706', accent: '#fbbf24' },
        'ESFP': { primary: '#db2777', secondary: '#f472b6', accent: '#fbcfe8' }
    };
    return colorMap[type] || colorMap['INTJ'];
}

function getPersonalityEmoji(type) {
    const emojiMap = {
        'INTJ': '🏗️', 'INTP': '🔬', 'ENTJ': '👑', 'ENTP': '💡',
        'INFJ': '🌟', 'INFP': '🎨', 'ENFJ': '🎭', 'ENFP': '🎪',
        'ISTJ': '📋', 'ISFJ': '🛡️', 'ESTJ': '⚖️', 'ESFJ': '🤝',
        'ISTP': '🔧', 'ISFP': '🌸', 'ESTP': '🏃', 'ESFP': '🎉'
    };
    return emojiMap[type] || '🧠';
}

function drawGeometricPattern(ctx, width, height, type) {
    // Different patterns for different personality types
    const patterns = {
        'NT': () => drawCirclePattern(ctx, width, height),
        'NF': () => drawWavePattern(ctx, width, height),
        'ST': () => drawGridPattern(ctx, width, height),
        'SF': () => drawHeartPattern(ctx, width, height)
    };

    const category = type.includes('N') ? (type.includes('T') ? 'NT' : 'NF') : (type.includes('T') ? 'ST' : 'SF');
    patterns[category]();
}

function drawCirclePattern(ctx, width, height) {
    for (let i = 0; i < width; i += 60) {
        for (let j = 0; j < height; j += 60) {
            ctx.beginPath();
            ctx.arc(i + 30, j + 30, 2, 0, 2 * Math.PI);
            ctx.fill();
        }
    }
}

function drawWavePattern(ctx, width, height) {
    ctx.beginPath();
    for (let x = 0; x < width; x += 10) {
        const y = height / 2 + Math.sin(x * 0.02) * 20;
        if (x === 0) ctx.moveTo(x, y);
        else ctx.lineTo(x, y);
    }
    ctx.stroke();
}

function drawGridPattern(ctx, width, height) {
    for (let i = 0; i < width; i += 40) {
        ctx.beginPath();
        ctx.moveTo(i, 0);
        ctx.lineTo(i, height);
        ctx.stroke();
    }
    for (let j = 0; j < height; j += 40) {
        ctx.beginPath();
        ctx.moveTo(0, j);
        ctx.lineTo(width, j);
        ctx.stroke();
    }
}

function drawHeartPattern(ctx, width, height) {
    for (let i = 0; i < width; i += 80) {
        for (let j = 0; j < height; j += 80) {
            drawHeart(ctx, i + 40, j + 40, 3);
        }
    }
}

function drawHeart(ctx, x, y, size) {
    ctx.beginPath();
    ctx.moveTo(x, y + size);
    ctx.bezierCurveTo(x, y, x - size, y, x - size, y + size);
    ctx.bezierCurveTo(x - size, y + size * 2, x, y + size * 2, x, y + size * 3);
    ctx.bezierCurveTo(x, y + size * 2, x + size, y + size * 2, x + size, y + size);
    ctx.bezierCurveTo(x + size, y, x, y, x, y + size);
    ctx.fill();
}

function drawDecorativeElements(ctx, width, height, colors) {
    // Add corner decorations
    ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';

    // Top left corner
    ctx.beginPath();
    ctx.arc(0, 0, 50, 0, Math.PI / 2);
    ctx.fill();

    // Bottom right corner
    ctx.beginPath();
    ctx.arc(width, height, 50, Math.PI, 3 * Math.PI / 2);
    ctx.fill();
}

function createShareSuccessEffect() {
    // Create floating hearts/stars effect
    for (let i = 0; i < 20; i++) {
        setTimeout(() => {
            createFloatingIcon();
        }, i * 100);
    }
}

function createFloatingIcon() {
    const icon = document.createElement('div');
    const icons = ['💫', '⭐', '✨', '🌟', '💖', '🎉'];
    const randomIcon = icons[Math.floor(Math.random() * icons.length)];

    icon.textContent = randomIcon;
    icon.style.cssText = `
        position: fixed;
        font-size: 20px;
        left: ${Math.random() * 100}vw;
        top: 100vh;
        z-index: 10002;
        pointer-events: none;
        animation: floatUp 3s ease-out forwards;
    `;

    document.body.appendChild(icon);
    setTimeout(() => icon.remove(), 3000);
}

function animateSocialProof() {
    const statItems = document.querySelectorAll('.stat-item');
    statItems.forEach((item, index) => {
        item.style.setProperty('--i', index + 1);
        item.style.animation = 'statFadeIn 0.6s ease-out both';
        item.style.animationDelay = `${index * 0.2}s`;
    });
}

// Add motivational messages during test
function showMotivationalMessage() {
    const messages = [
        "You're doing great! Keep going! 🌟",
        "Almost there! Your insights await! 🚀",
        "Excellent progress! Stay focused! 💪",
        "You're uncovering amazing insights! ✨",
        "Keep it up! Your personality is unique! 🎯"
    ];

    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    const messageElement = document.createElement('div');
    messageElement.className = 'motivational-message';
    messageElement.textContent = randomMessage;
    messageElement.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        font-weight: 500;
        font-size: 14px;
        z-index: 1000;
        animation: messageSlideIn 0.5s ease-out, messageSlideOut 0.5s ease-in 2.5s forwards;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    `;

    document.body.appendChild(messageElement);
    setTimeout(() => messageElement.remove(), 3000);
}

// Enhanced question navigation with motivational feedback
function nextQuestion() {
    if (testState.currentQuestion < questions.length - 1) {
        // Show motivational message every 5 questions
        if ((testState.currentQuestion + 1) % 5 === 0) {
            showMotivationalMessage();
        }

        testState.currentQuestion++;
        displayQuestion();
    } else {
        // Finish test with celebration
        showCompletionCelebration();
        setTimeout(() => {
            finishTest();
        }, 1000);
    }
}

function showCompletionCelebration() {
    const celebration = document.createElement('div');
    celebration.innerHTML = `
        <div class="completion-celebration">
            <div class="celebration-content">
                <div class="celebration-icon">🎉</div>
                <h2>Congratulations!</h2>
                <p>You've completed the assessment!</p>
                <div class="celebration-sparkles">✨ ⭐ ✨</div>
            </div>
        </div>
    `;
    celebration.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        animation: celebrationFadeIn 0.5s ease-out;
    `;

    const style = document.createElement('style');
    style.textContent = `
        .completion-celebration {
            background: linear-gradient(135deg, #667eea, #764ba2);
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            color: white;
            animation: celebrationBounce 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        .celebration-icon {
            font-size: 60px;
            margin-bottom: 20px;
            animation: iconSpin 1s ease-out;
        }
        .celebration-content h2 {
            margin: 0 0 10px 0;
            font-size: 28px;
        }
        .celebration-content p {
            margin: 0 0 20px 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .celebration-sparkles {
            font-size: 24px;
            animation: sparkleFloat 2s ease-in-out infinite;
        }
        @keyframes celebrationFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes celebrationBounce {
            0% { transform: scale(0.3) rotate(-10deg); }
            50% { transform: scale(1.1) rotate(5deg); }
            100% { transform: scale(1) rotate(0deg); }
        }
        @keyframes iconSpin {
            0% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.2); }
            100% { transform: rotate(360deg) scale(1); }
        }
        @keyframes sparkleFloat {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }
        @keyframes messageSlideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes messageSlideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;

    document.head.appendChild(style);
    document.body.appendChild(celebration);

    setTimeout(() => {
        celebration.remove();
        style.remove();
    }, 1000);
}

function generateShareLink() {
    const result = testState.personalityResult;
    const shareData = {
        type: result.type,
        name: result.typeData.name,
        description: result.typeData.description
    };
    
    // Create share URL (in a real app, this would be a proper share endpoint)
    const shareUrl = `${window.location.origin}${window.location.pathname}?shared=${btoa(JSON.stringify(shareData))}`;
    
    // Copy to clipboard
    if (navigator.clipboard) {
        navigator.clipboard.writeText(shareUrl).then(() => {
            showShareFeedback('Share link copied to clipboard!');
        });
    } else {
        // Fallback
        const textArea = document.createElement('textarea');
        textArea.value = shareUrl;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showShareFeedback('Share link copied to clipboard!');
    }
}

function downloadPdfReport() {
    // In a real app, this would generate a proper PDF
    showShareFeedback('PDF report feature coming soon!');
}

function shareOnSocial(platform) {
    const result = testState.personalityResult;
    const emoji = getPersonalityEmoji(result.type);
    const text = `${emoji} I just discovered I'm an ${result.type} (${result.typeData.name}) on PersonalityPro! ${result.typeData.description}`;
    const url = window.location.href;
    const hashtags = 'PersonalityTest,MBTI,SelfDiscovery,Psychology';

    let shareUrl = '';

    switch (platform) {
        case 'twitter':
            shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}&hashtags=${hashtags}`;
            break;
        case 'facebook':
            shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}&quote=${encodeURIComponent(text)}`;
            break;
        case 'linkedin':
            shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}&summary=${encodeURIComponent(text)}`;
            break;
        case 'whatsapp':
            shareUrl = `https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`;
            break;
        case 'instagram':
            // Instagram doesn't support direct URL sharing, so copy text to clipboard
            copyToClipboard(text + ' ' + url);
            showShareFeedback('Text copied! Open Instagram and paste in your story or post 📸');
            return;
        case 'tiktok':
            // TikTok doesn't support direct URL sharing, so copy text to clipboard
            copyToClipboard(text + ' ' + url);
            showShareFeedback('Text copied! Create a TikTok video about your personality type! 🎬');
            return;
    }

    if (shareUrl) {
        // Add click animation
        const button = document.getElementById(`share${platform.charAt(0).toUpperCase() + platform.slice(1)}`);
        button.style.animation = 'buttonPulse 0.3s ease-out';
        setTimeout(() => button.style.animation = '', 300);

        window.open(shareUrl, '_blank', 'width=600,height=400');
        showShareFeedback(`Opening ${platform.charAt(0).toUpperCase() + platform.slice(1)}... 🚀`);
    }
}

function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text);
    } else {
        // Fallback
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
    }
}

function closeShareModal() {
    document.getElementById('shareImageModal').classList.remove('active');
}

function downloadShareImage() {
    const canvas = document.getElementById('shareCanvas');
    const link = document.createElement('a');
    link.download = `personality-${testState.personalityResult.type}.png`;
    link.href = canvas.toDataURL();
    link.click();
}

function copyImageToClipboard() {
    const canvas = document.getElementById('shareCanvas');
    canvas.toBlob(blob => {
        const item = new ClipboardItem({ 'image/png': blob });
        navigator.clipboard.write([item]).then(() => {
            showShareFeedback('Image copied to clipboard!');
            closeShareModal();
        }).catch(() => {
            showShareFeedback('Unable to copy image to clipboard');
        });
    });
}

function createCelebrationEffect() {
    // Create confetti effect
    for (let i = 0; i < 50; i++) {
        createConfetti();
    }

    // Add sparkle effect to personality badge
    const badge = document.querySelector('.personality-badge');
    const sparkles = document.createElement('div');
    sparkles.className = 'sparkles';
    sparkles.innerHTML = '✨✨✨';
    sparkles.style.cssText = `
        position: absolute;
        top: -20px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 24px;
        animation: sparkle 2s ease-out;
        pointer-events: none;
    `;
    badge.appendChild(sparkles);

    setTimeout(() => sparkles.remove(), 2000);
}

function createConfetti() {
    const confetti = document.createElement('div');
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3'];
    const color = colors[Math.floor(Math.random() * colors.length)];

    confetti.style.cssText = `
        position: fixed;
        width: 10px;
        height: 10px;
        background: ${color};
        left: ${Math.random() * 100}vw;
        top: -10px;
        z-index: 10000;
        border-radius: 50%;
        animation: confettiFall ${2 + Math.random() * 3}s linear forwards;
        transform: rotate(${Math.random() * 360}deg);
    `;

    document.body.appendChild(confetti);

    setTimeout(() => confetti.remove(), 5000);
}

function showShareFeedback(message) {
    const feedback = document.createElement('div');
    feedback.className = 'share-feedback';
    feedback.innerHTML = `
        <i class="fas fa-check-circle"></i>
        <span>${message}</span>
    `;
    feedback.style.cssText = `
        position: fixed;
        top: 100px;
        left: 50%;
        transform: translateX(-50%) translateY(-20px);
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        padding: 16px 24px;
        border-radius: 12px;
        z-index: 10001;
        font-weight: 500;
        box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
        display: flex;
        align-items: center;
        gap: 8px;
        animation: feedbackSlideIn 0.5s ease-out;
    `;

    document.body.appendChild(feedback);

    setTimeout(() => {
        feedback.style.animation = 'feedbackSlideOut 0.5s ease-in forwards';
        setTimeout(() => feedback.remove(), 500);
    }, 2500);
}

// Check for shared results on page load
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const sharedData = urlParams.get('shared');
    
    if (sharedData) {
        try {
            const shareData = JSON.parse(atob(sharedData));
            // Display shared result (simplified version)
            showSharedResult(shareData);
        } catch (e) {
            console.error('Invalid shared data');
        }
    }
});

function showSharedResult(shareData) {
    // Create a simplified results display for shared results
    document.getElementById('personalityType').textContent = shareData.type;
    document.getElementById('personalityName').textContent = shareData.name;
    document.getElementById('personalityDescription').textContent = shareData.description;
    
    // Hide some sections that require full test data
    document.querySelector('.dimensions-section').style.display = 'none';
    document.querySelector('.share-section').style.display = 'none';
    
    showScreen('resultsScreen');
}
