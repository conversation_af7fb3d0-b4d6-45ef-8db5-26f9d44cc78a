// About page specific JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeAboutAnimations();
    initializeProcessSteps();
    initializeAIVisualization();
    initializeScrollAnimations();
});

// Initialize about page animations
function initializeAboutAnimations() {
    const heroContent = document.querySelector('.hero-content');
    
    if (heroContent) {
        // Animate hero content on load
        setTimeout(() => {
            heroContent.style.opacity = '1';
            heroContent.style.transform = 'translateY(0)';
        }, 300);
    }
}

// Initialize process steps interactions
function initializeProcessSteps() {
    const steps = document.querySelectorAll('.step');
    
    steps.forEach((step, index) => {
        // Add hover effects
        step.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        step.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
        
        // Add click interaction
        step.addEventListener('click', function() {
            showProcessDetail(index + 1);
        });
        
        // Animate steps on scroll
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.classList.add('animate-in');
                    }, index * 200);
                }
            });
        }, { threshold: 0.3 });
        
        observer.observe(step);
    });
}

// Show process detail modal
function showProcessDetail(stepNumber) {
    const processDetails = {
        1: {
            title: "Answer Thoughtful Questions",
            description: "Our assessment consists of carefully crafted questions designed to understand your personality preferences. Each question is based on established psychological research and targets specific aspects of your personality.",
            details: [
                "60 scientifically-designed questions",
                "Covers all four personality dimensions",
                "Takes approximately 10-15 minutes",
                "No right or wrong answers"
            ]
        },
        2: {
            title: "Advanced AI Analysis",
            description: "Our sophisticated AI algorithms analyze your responses using multiple psychological frameworks. The system considers patterns, consistency, and nuances in your answers to provide accurate results.",
            details: [
                "Natural language processing",
                "Pattern recognition algorithms",
                "Cross-validation with multiple models",
                "Continuous learning and improvement"
            ]
        },
        3: {
            title: "Comprehensive Results",
            description: "Receive a detailed personality report that goes beyond just your type. Our AI generates personalized insights, strengths analysis, and growth recommendations tailored specifically to you.",
            details: [
                "Detailed personality type description",
                "Strengths and potential blind spots",
                "Career and relationship insights",
                "Personalized growth recommendations"
            ]
        },
        4: {
            title: "Apply Your Insights",
            description: "Use your newfound self-awareness to make better decisions, improve relationships, and achieve personal growth. Our platform provides ongoing resources and tools to help you apply your insights.",
            details: [
                "Career guidance and suggestions",
                "Relationship compatibility insights",
                "Personal development resources",
                "Ongoing support and updates"
            ]
        }
    };
    
    const detail = processDetails[stepNumber];
    if (detail) {
        showModal(detail);
    }
}

// Show modal with process details
function showModal(detail) {
    // Remove existing modal if any
    const existingModal = document.querySelector('.process-modal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Create modal
    const modal = document.createElement('div');
    modal.className = 'process-modal';
    modal.innerHTML = `
        <div class="modal-overlay">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${detail.title}</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <p class="process-description">${detail.description}</p>
                    <div class="process-details">
                        <h4>Key Features:</h4>
                        <ul>
                            ${detail.details.map(item => `<li>${item}</li>`).join('')}
                        </ul>
                    </div>
                    <div class="modal-actions">
                        <a href="test.html" class="btn btn-primary">Start Assessment</a>
                        <button class="btn btn-secondary modal-close">Close</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Add event listeners
    const closeButtons = modal.querySelectorAll('.modal-close');
    const overlay = modal.querySelector('.modal-overlay');
    
    closeButtons.forEach(button => {
        button.addEventListener('click', () => modal.remove());
    });
    
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            modal.remove();
        }
    });
    
    // Animate modal in
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

// Initialize AI visualization
function initializeAIVisualization() {
    const aiNodes = document.querySelectorAll('.ai-node');
    
    if (aiNodes.length > 0) {
        // Add random activation to nodes
        setInterval(() => {
            aiNodes.forEach(node => {
                node.classList.remove('active');
            });
            
            // Randomly activate 2-3 nodes
            const activeCount = Math.floor(Math.random() * 2) + 2;
            const shuffled = Array.from(aiNodes).sort(() => 0.5 - Math.random());
            
            for (let i = 0; i < activeCount; i++) {
                shuffled[i].classList.add('active');
            }
        }, 3000);
    }
}

// Initialize scroll animations
function initializeScrollAnimations() {
    const animatedElements = document.querySelectorAll('.value-item, .visual-card, .feature, .team-member, .copyright-item');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    animatedElements.forEach(element => {
        element.classList.add('animate-on-scroll');
        observer.observe(element);
    });
}

// Smooth scroll to disclaimer section
function scrollToDisclaimer() {
    const disclaimerSection = document.getElementById('disclaimer');
    if (disclaimerSection) {
        Utils.scrollTo(disclaimerSection, 80);
    }
}

// Add click handler for disclaimer links
document.addEventListener('click', function(e) {
    if (e.target.getAttribute('href') === '#disclaimer') {
        e.preventDefault();
        scrollToDisclaimer();
    }
});

// Add CSS for modal and animations
const aboutStyles = `
<style>
.process-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.process-modal.show {
    opacity: 1;
    visibility: visible;
}

.process-modal .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.process-modal .modal-content {
    background: white;
    border-radius: 12px;
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    transform: translateY(20px);
    transition: transform 0.3s ease;
}

.process-modal.show .modal-content {
    transform: translateY(0);
}

.process-modal .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.process-modal .modal-header h3 {
    margin: 0;
    color: var(--primary-color);
}

.process-modal .modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.process-modal .modal-body {
    padding: 20px;
}

.process-description {
    font-size: 16px;
    margin-bottom: 20px;
    color: #374151;
    line-height: 1.6;
}

.process-details h4 {
    margin-bottom: 10px;
    color: #1f2937;
}

.process-details ul {
    list-style: none;
    padding: 0;
}

.process-details li {
    padding: 8px 0;
    color: #6b7280;
    position: relative;
    padding-left: 25px;
}

.process-details li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-weight: bold;
}

.process-modal .modal-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    justify-content: flex-end;
}

/* Animation classes */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.hero-content {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .process-modal .modal-content {
        margin: 10px;
        max-width: calc(100% - 20px);
    }
    
    .process-modal .modal-actions {
        flex-direction: column;
    }
    
    .process-modal .modal-actions .btn {
        width: 100%;
        justify-content: center;
    }
}
</style>
`;

// Add styles to head
document.head.insertAdjacentHTML('beforeend', aboutStyles);
