/* Common Styles - Shared across all pages */

/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #818cf8;
    --secondary-color: #f59e0b;
    --accent-color: #10b981;
    --danger-color: #ef4444;
    
    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e <PERSON>I', <PERSON>o, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

/* Base Styles */
html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

@media (min-width: 768px) {
    .container {
        padding: 0 var(--spacing-6);
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 var(--spacing-8);
    }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    color: var(--gray-900);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

@media (min-width: 768px) {
    h1 { font-size: var(--font-size-5xl); }
    h2 { font-size: var(--font-size-4xl); }
    h3 { font-size: var(--font-size-3xl); }
}

p {
    margin-bottom: var(--spacing-4);
    color: var(--gray-600);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-sm);
    font-weight: 500;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    color: var(--white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background-color: var(--white);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
    background-color: var(--gray-50);
    color: var(--gray-900);
    border-color: var(--gray-400);
}

.btn-large {
    padding: var(--spacing-4) var(--spacing-8);
    font-size: var(--font-size-base);
}

.btn-small {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-xs);
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--gray-200);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4) var(--spacing-6);
    max-width: 1200px;
    margin: 0 auto;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--gray-900);
}

.brand-icon {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
}

.nav-menu {
    display: none;
    gap: var(--spacing-8);
}

.nav-link {
    font-weight: 500;
    color: var(--gray-600);
    transition: color var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--primary-color);
    border-radius: var(--radius-full);
}

.nav-toggle {
    display: flex;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;
    padding: var(--spacing-2);
}

.nav-toggle span {
    width: 24px;
    height: 2px;
    background-color: var(--gray-700);
    transition: all var(--transition-fast);
}

@media (min-width: 768px) {
    .nav-menu {
        display: flex;
    }
    
    .nav-toggle {
        display: none;
    }
}

/* Footer */
.footer {
    background-color: var(--gray-900);
    color: var(--gray-300);
    padding: var(--spacing-16) 0 var(--spacing-8);
    margin-top: var(--spacing-20);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
}

@media (min-width: 768px) {
    .footer-content {
        grid-template-columns: 2fr 1fr 1fr 1fr;
    }
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--white);
    margin-bottom: var(--spacing-4);
}

.footer-description {
    color: var(--gray-400);
    margin-bottom: var(--spacing-6);
}

.social-links {
    display: flex;
    gap: var(--spacing-4);
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--gray-800);
    color: var(--gray-400);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
}

.social-link:hover {
    background-color: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

.footer-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--white);
    margin-bottom: var(--spacing-4);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-2);
}

.footer-links a {
    color: var(--gray-400);
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--white);
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    padding-top: var(--spacing-8);
    margin-top: var(--spacing-8);
    text-align: center;
    color: var(--gray-500);
    font-size: var(--font-size-sm);
}

.disclaimer {
    margin-top: var(--spacing-2);
    font-size: var(--font-size-xs);
    color: var(--gray-600);
}

/* Utility Classes */
.gradient-text {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-4 { margin-bottom: var(--spacing-4); }
.mb-8 { margin-bottom: var(--spacing-8); }
.mt-4 { margin-top: var(--spacing-4); }
.mt-8 { margin-top: var(--spacing-8); }

.hidden { display: none; }
.block { display: block; }
.flex { display: flex; }
.grid { display: grid; }

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Utilities */
@media (max-width: 767px) {
    .hidden-mobile { display: none !important; }
}

@media (min-width: 768px) {
    .hidden-desktop { display: none !important; }
}
