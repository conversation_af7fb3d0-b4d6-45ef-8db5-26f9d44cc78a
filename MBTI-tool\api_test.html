<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pollinations AI API 测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #555;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            background-color: #e9ecef;
            border-left: 4px solid #007bff;
        }
        .error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .success {
            background-color: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .loading {
            background-color: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .image-result {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin-top: 10px;
        }
        .models-list {
            max-height: 200px;
            overflow-y: auto;
            background: white;
            padding: 10px;
            border-radius: 4px;
        }
        .model-item {
            padding: 8px;
            margin: 4px 0;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }
        .model-name {
            font-weight: bold;
            color: #007bff;
        }
        .model-desc {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Pollinations AI API 测试工具</h1>
        
        <!-- 模型列表 -->
        <div class="test-section">
            <h2>📋 可用模型</h2>
            <button onclick="loadModels()">获取文本模型列表</button>
            <button onclick="loadImageModels()">获取图像模型列表</button>
            <div id="modelsResult" class="result" style="display:none;"></div>
        </div>

        <!-- 简单文本生成 -->
        <div class="test-section">
            <h2>💬 简单文本生成 (GET)</h2>
            <div class="input-group">
                <label for="simplePrompt">输入提示词:</label>
                <input type="text" id="simplePrompt" placeholder="例如: Hello World" value="你好，世界！">
            </div>
            <button onclick="generateSimpleText()">生成文本</button>
            <div id="simpleTextResult" class="result" style="display:none;"></div>
        </div>

        <!-- 高级文本生成 -->
        <div class="test-section">
            <h2>🧠 高级文本生成 (POST)</h2>
            <div class="input-group">
                <label for="advancedModel">选择模型:</label>
                <select id="advancedModel">
                    <option value="openai">OpenAI GPT-4.1 Nano</option>
                    <option value="mistral">Mistral Small 3.1 24B</option>
                    <option value="llama-roblox">Llama 3.1 8B</option>
                    <option value="qwen-coder">Qwen 2.5 Coder 32B</option>
                </select>
            </div>
            <div class="input-group">
                <label for="advancedPrompt">输入消息:</label>
                <textarea id="advancedPrompt" rows="3" placeholder="例如: 请介绍一下人工智能的发展历史">请用中文介绍一下MBTI人格测试</textarea>
            </div>
            <button onclick="generateAdvancedText()">生成文本</button>
            <button onclick="generateOpenAICompatible()">使用OpenAI兼容接口</button>
            <div id="advancedTextResult" class="result" style="display:none;"></div>
        </div>

        <!-- 图像生成 -->
        <div class="test-section">
            <h2>🎨 图像生成</h2>
            <div class="input-group">
                <label for="imagePrompt">图像描述:</label>
                <input type="text" id="imagePrompt" placeholder="例如: a beautiful sunset over mountains" value="一只可爱的小猫在花园里玩耍">
            </div>
            <button onclick="generateImage()">生成图像</button>
            <div id="imageResult" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        // 显示结果的通用函数
        function showResult(elementId, content, type = 'success') {
            const element = document.getElementById(elementId);
            element.innerHTML = content;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // 显示加载状态
        function showLoading(elementId, message = '正在处理...') {
            showResult(elementId, `⏳ ${message}`, 'loading');
        }

        // 获取模型列表
        async function loadModels() {
            showLoading('modelsResult', '正在获取文本模型列表...');
            try {
                const response = await fetch('https://text.pollinations.ai/models');
                const models = await response.json();
                
                let html = '<div class="models-list"><h3>文本生成模型:</h3>';
                models.forEach(model => {
                    html += `
                        <div class="model-item">
                            <div class="model-name">${model.name}</div>
                            <div class="model-desc">${model.description}</div>
                        </div>
                    `;
                });
                html += '</div>';
                
                showResult('modelsResult', html, 'success');
            } catch (error) {
                showResult('modelsResult', `❌ 获取模型列表失败: ${error.message}`, 'error');
            }
        }

        // 获取图像模型列表
        async function loadImageModels() {
            showLoading('modelsResult', '正在获取图像模型列表...');
            try {
                const response = await fetch('https://image.pollinations.ai/models');
                const models = await response.json();
                
                let html = '<div class="models-list"><h3>图像生成模型:</h3>';
                models.forEach(model => {
                    html += `
                        <div class="model-item">
                            <div class="model-name">${model}</div>
                        </div>
                    `;
                });
                html += '</div>';
                
                showResult('modelsResult', html, 'success');
            } catch (error) {
                showResult('modelsResult', `❌ 获取图像模型列表失败: ${error.message}`, 'error');
            }
        }

        // 简单文本生成
        async function generateSimpleText() {
            const prompt = document.getElementById('simplePrompt').value;
            if (!prompt.trim()) {
                showResult('simpleTextResult', '❌ 请输入提示词', 'error');
                return;
            }

            showLoading('simpleTextResult', '正在生成文本...');
            try {
                const encodedPrompt = encodeURIComponent(prompt);
                const response = await fetch(`https://text.pollinations.ai/${encodedPrompt}`);
                const result = await response.text();
                
                showResult('simpleTextResult', `<strong>生成结果:</strong><br>${result}`, 'success');
            } catch (error) {
                showResult('simpleTextResult', `❌ 生成失败: ${error.message}`, 'error');
            }
        }

        // 高级文本生成
        async function generateAdvancedText() {
            const model = document.getElementById('advancedModel').value;
            const prompt = document.getElementById('advancedPrompt').value;
            
            if (!prompt.trim()) {
                showResult('advancedTextResult', '❌ 请输入消息内容', 'error');
                return;
            }

            showLoading('advancedTextResult', '正在生成文本...');
            try {
                const response = await fetch('https://text.pollinations.ai/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        messages: [
                            {
                                role: 'user',
                                content: prompt
                            }
                        ],
                        model: model
                    })
                });
                
                const result = await response.text();
                showResult('advancedTextResult', `<strong>模型:</strong> ${model}<br><strong>生成结果:</strong><br>${result}`, 'success');
            } catch (error) {
                showResult('advancedTextResult', `❌ 生成失败: ${error.message}`, 'error');
            }
        }

        // OpenAI兼容接口
        async function generateOpenAICompatible() {
            const model = document.getElementById('advancedModel').value;
            const prompt = document.getElementById('advancedPrompt').value;
            
            if (!prompt.trim()) {
                showResult('advancedTextResult', '❌ 请输入消息内容', 'error');
                return;
            }

            showLoading('advancedTextResult', '正在使用OpenAI兼容接口生成...');
            try {
                const response = await fetch('https://text.pollinations.ai/openai', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        messages: [
                            {
                                role: 'user',
                                content: prompt
                            }
                        ],
                        model: model
                    })
                });
                
                const result = await response.json();
                const content = result.choices[0].message.content;
                
                showResult('advancedTextResult', 
                    `<strong>OpenAI兼容接口结果:</strong><br>
                     <strong>模型:</strong> ${result.model}<br>
                     <strong>内容:</strong><br>${content}<br>
                     <strong>Token使用:</strong> ${result.usage.total_tokens} (输入: ${result.usage.prompt_tokens}, 输出: ${result.usage.completion_tokens})`, 
                    'success');
            } catch (error) {
                showResult('advancedTextResult', `❌ 生成失败: ${error.message}`, 'error');
            }
        }

        // 图像生成
        async function generateImage() {
            const prompt = document.getElementById('imagePrompt').value;
            if (!prompt.trim()) {
                showResult('imageResult', '❌ 请输入图像描述', 'error');
                return;
            }

            showLoading('imageResult', '正在生成图像...');
            try {
                const encodedPrompt = encodeURIComponent(prompt);
                const imageUrl = `https://image.pollinations.ai/prompt/${encodedPrompt}`;
                
                showResult('imageResult', 
                    `<strong>生成的图像:</strong><br>
                     <img src="${imageUrl}" alt="Generated Image" class="image-result" onload="this.style.display='block'" onerror="this.style.display='none'; this.parentNode.innerHTML='❌ 图像加载失败'">
                     <br><strong>图像URL:</strong> <a href="${imageUrl}" target="_blank">${imageUrl}</a>`, 
                    'success');
            } catch (error) {
                showResult('imageResult', `❌ 生成失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动获取模型列表
        window.addEventListener('load', function() {
            loadModels();
        });
    </script>
</body>
</html>
