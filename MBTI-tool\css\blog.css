/* Blog Page Specific Styles */

/* Blog Hero Section */
.blog-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.blog-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="blog-pattern" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="6.25" cy="6.25" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23blog-pattern)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 700px;
    margin: 0 auto;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    color: var(--white);
    margin-bottom: var(--spacing-6);
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin-bottom: 0;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-16);
}

.section-title {
    margin-bottom: var(--spacing-4);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

/* Featured Articles */
.featured-articles {
    padding: var(--spacing-20) 0;
    background: var(--white);
}

.featured-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
}

@media (min-width: 768px) {
    .featured-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .featured-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.featured-article {
    background: var(--white);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.featured-article:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.article-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.featured-article:hover .article-image img {
    transform: scale(1.05);
}

.article-category {
    position: absolute;
    top: var(--spacing-4);
    left: var(--spacing-4);
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.article-content {
    padding: var(--spacing-6);
}

.article-title {
    margin-bottom: var(--spacing-4);
}

.article-title a {
    color: var(--gray-900);
    text-decoration: none;
    font-size: var(--font-size-lg);
    font-weight: 600;
    line-height: 1.3;
    transition: color var(--transition-fast);
}

.article-title a:hover {
    color: var(--primary-color);
}

.article-excerpt {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-6);
}

.article-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.article-meta span {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

/* Article Categories */
.article-categories {
    padding: var(--spacing-20) 0;
    background: var(--gray-50);
}

.categories-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
}

@media (min-width: 768px) {
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .categories-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.category-card {
    background: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    text-align: center;
    transition: all var(--transition-normal);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
    transition: left var(--transition-slow);
}

.category-card:hover::before {
    left: 100%;
}

.category-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.category-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-6);
    font-size: var(--font-size-2xl);
    color: var(--white);
    position: relative;
    z-index: 1;
}

.category-title {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-4);
    color: var(--gray-900);
    position: relative;
    z-index: 1;
}

.category-description {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-6);
    position: relative;
    z-index: 1;
}

.category-stats {
    position: relative;
    z-index: 1;
}

.article-count {
    background: var(--gray-100);
    color: var(--gray-700);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* Recent Articles */
.recent-articles {
    padding: var(--spacing-20) 0;
    background: var(--white);
}

.articles-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-12);
}

.article-item {
    display: flex;
    gap: var(--spacing-6);
    background: var(--white);
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.article-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.article-thumbnail {
    flex-shrink: 0;
    width: 120px;
    height: 120px;
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.article-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.article-item:hover .article-thumbnail img {
    transform: scale(1.05);
}

.article-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.article-category-tag {
    display: inline-block;
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    width: fit-content;
    margin-bottom: var(--spacing-3);
}

.article-info .article-title {
    margin-bottom: var(--spacing-3);
}

.article-info .article-title a {
    font-size: var(--font-size-base);
    font-weight: 600;
}

.article-info .article-excerpt {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-4);
    flex: 1;
}

.article-info .article-meta {
    margin-top: auto;
}

.load-more-container {
    text-align: center;
}

.load-more-btn {
    padding: var(--spacing-4) var(--spacing-8);
}

/* Newsletter Section */
.newsletter {
    padding: var(--spacing-20) 0;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.newsletter-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
    align-items: center;
    text-align: center;
}

@media (min-width: 1024px) {
    .newsletter-content {
        grid-template-columns: 1fr 1fr;
        text-align: left;
    }
}

.newsletter-title {
    font-size: var(--font-size-3xl);
    color: var(--white);
    margin-bottom: var(--spacing-4);
}

.newsletter-description {
    font-size: var(--font-size-lg);
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin-bottom: 0;
}

.signup-form .form-group {
    display: flex;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-3);
}

.email-input {
    flex: 1;
    padding: var(--spacing-4);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    font-size: var(--font-size-base);
    backdrop-filter: blur(10px);
}

.email-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.email-input:focus {
    outline: none;
    border-color: var(--white);
    background: rgba(255, 255, 255, 0.2);
}

.signup-form .btn {
    padding: var(--spacing-4) var(--spacing-6);
    background: var(--white);
    color: var(--primary-color);
    border: none;
    white-space: nowrap;
}

.signup-form .btn:hover {
    background: var(--gray-100);
    color: var(--primary-dark);
}

.form-note {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .featured-grid {
        grid-template-columns: 1fr;
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
    }
    
    .article-item {
        flex-direction: column;
    }
    
    .article-thumbnail {
        width: 100%;
        height: 200px;
    }
    
    .signup-form .form-group {
        flex-direction: column;
    }
    
    .signup-form .btn {
        width: 100%;
        justify-content: center;
    }
}

/* Animation Classes */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Loading States */
.loading-articles {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-12);
}

.loading-articles .loading {
    width: 40px;
    height: 40px;
    border-width: 4px;
}

/* Article Hover Effects */
.featured-article,
.article-item {
    cursor: pointer;
}

.featured-article:hover .article-title a,
.article-item:hover .article-title a {
    color: var(--primary-color);
}

/* Category Hover Effects */
.category-card {
    cursor: pointer;
}

.category-card:hover .category-icon {
    transform: scale(1.1);
}

.category-card:hover .article-count {
    background: var(--primary-color);
    color: var(--white);
}
