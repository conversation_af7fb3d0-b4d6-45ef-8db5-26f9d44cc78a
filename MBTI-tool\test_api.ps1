# Test Pollinations AI APIs

Write-Host "Testing Pollinations AI APIs..." -ForegroundColor Green

# Test 1: Text Models List
Write-Host "`n1. Testing Text Models List..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "https://text.pollinations.ai/models" -Method GET
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Image Models List  
Write-Host "`n2. Testing Image Models List..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "https://image.pollinations.ai/models" -Method GET
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Simple Text Generation (GET)
Write-Host "`n3. Testing Simple Text Generation (GET)..." -ForegroundColor Yellow
try {
    $prompt = "Hello%20World"
    $response = Invoke-WebRequest -Uri "https://text.pollinations.ai/$prompt" -Method GET
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Image Generation
Write-Host "`n4. Testing Image Generation..." -ForegroundColor Yellow
try {
    $prompt = "a%20beautiful%20sunset%20over%20mountains"
    $response = Invoke-WebRequest -Uri "https://image.pollinations.ai/prompt/$prompt" -Method GET
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Content Type: $($response.Headers.'Content-Type')" -ForegroundColor Cyan
    Write-Host "Content Length: $($response.RawContentLength) bytes" -ForegroundColor Cyan
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Advanced Text Generation (POST)
Write-Host "`n5. Testing Advanced Text Generation (POST)..." -ForegroundColor Yellow
try {
    $body = @{
        messages = @(
            @{
                role = "user"
                content = "Hello, how are you?"
            }
        )
        model = "openai"
    } | ConvertTo-Json -Depth 3
    
    $response = Invoke-WebRequest -Uri "https://text.pollinations.ai/" -Method POST -Body $body -ContentType "application/json"
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: OpenAI Compatible Endpoint
Write-Host "`n6. Testing OpenAI Compatible Endpoint..." -ForegroundColor Yellow
try {
    $body = @{
        messages = @(
            @{
                role = "user"
                content = "What is the capital of France?"
            }
        )
        model = "openai"
    } | ConvertTo-Json -Depth 3
    
    $response = Invoke-WebRequest -Uri "https://text.pollinations.ai/openai" -Method POST -Body $body -ContentType "application/json"
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nAPI Testing Complete!" -ForegroundColor Green
