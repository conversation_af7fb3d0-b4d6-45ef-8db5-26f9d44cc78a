/* About Page Specific Styles */

/* About Hero Section */
.about-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.about-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    color: var(--white);
    margin-bottom: var(--spacing-6);
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin-bottom: 0;
}

/* Mission Section */
.mission {
    padding: var(--spacing-20) 0;
    background: var(--white);
}

.mission-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-12);
    align-items: center;
}

@media (min-width: 1024px) {
    .mission-content {
        grid-template-columns: 2fr 1fr;
    }
}

.mission-description {
    font-size: var(--font-size-lg);
    color: var(--gray-700);
    margin-bottom: var(--spacing-6);
    line-height: 1.7;
}

.mission-values {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
    margin-top: var(--spacing-8);
}

@media (min-width: 768px) {
    .mission-values {
        grid-template-columns: repeat(3, 1fr);
    }
}

.value-item {
    text-align: center;
    padding: var(--spacing-6);
    background: var(--gray-50);
    border-radius: var(--radius-xl);
    transition: all var(--transition-normal);
}

.value-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    background: var(--white);
}

.value-item i {
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-4);
}

.value-item h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-2);
    color: var(--gray-900);
}

.value-item p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin-bottom: 0;
}

.mission-visual {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
}

.visual-card {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    text-align: center;
    transition: all var(--transition-normal);
}

.visual-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.visual-card i {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-4);
}

.visual-card h3 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-2);
    color: var(--white);
}

.visual-card p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
}

/* How It Works Section */
.how-it-works {
    padding: var(--spacing-20) 0;
    background: var(--gray-50);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-16);
}

.section-title {
    margin-bottom: var(--spacing-4);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

.process-steps {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
}

@media (min-width: 768px) {
    .process-steps {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .process-steps {
        grid-template-columns: repeat(4, 1fr);
    }
}

.step {
    background: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    text-align: center;
    position: relative;
    transition: all var(--transition-normal);
    border: 2px solid transparent;
}

.step:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin: 0 auto var(--spacing-6);
}

.step-content h3 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-4);
    color: var(--gray-900);
}

.step-content p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 0;
}

/* Technology Section */
.technology {
    padding: var(--spacing-20) 0;
    background: var(--white);
}

.tech-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-12);
    align-items: center;
}

@media (min-width: 1024px) {
    .tech-content {
        grid-template-columns: 1fr 1fr;
    }
}

.tech-description {
    font-size: var(--font-size-lg);
    color: var(--gray-700);
    margin-bottom: var(--spacing-8);
    line-height: 1.7;
}

.tech-features {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
}

.feature {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-4);
    padding: var(--spacing-4);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
}

.feature:hover {
    background: var(--white);
    box-shadow: var(--shadow-md);
    transform: translateX(4px);
}

.feature i {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
    margin-top: var(--spacing-1);
}

.feature h4 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-2);
    color: var(--gray-900);
}

.feature p {
    color: var(--gray-600);
    margin-bottom: 0;
}

/* AI Visualization */
.tech-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.ai-visualization {
    position: relative;
    width: 300px;
    height: 300px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.ai-node {
    position: absolute;
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-full);
    animation: pulse 2s infinite;
}

.ai-node.active {
    background: var(--white);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.ai-node:nth-child(1) { top: 20%; left: 20%; animation-delay: 0s; }
.ai-node:nth-child(2) { top: 20%; right: 20%; animation-delay: 0.4s; }
.ai-node:nth-child(3) { bottom: 20%; left: 20%; animation-delay: 0.8s; }
.ai-node:nth-child(4) { bottom: 20%; right: 20%; animation-delay: 1.2s; }
.ai-node:nth-child(5) { top: 50%; left: 50%; transform: translate(-50%, -50%); animation-delay: 1.6s; }

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.2); opacity: 1; }
}

/* Team Section */
.team {
    padding: var(--spacing-20) 0;
    background: var(--gray-50);
}

.team-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
    justify-items: center;
}

.team-member {
    background: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    text-align: center;
    max-width: 400px;
    transition: all var(--transition-normal);
    border: 2px solid transparent;
}

.team-member:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.member-avatar {
    width: 120px;
    height: 120px;
    margin: 0 auto var(--spacing-6);
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-full);
    color: var(--white);
    font-size: var(--font-size-5xl);
}

.member-name {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-2);
    color: var(--gray-900);
}

.member-role {
    font-size: var(--font-size-base);
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: var(--spacing-4);
}

.member-bio {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 0;
}

/* Disclaimer Section */
.disclaimer {
    padding: var(--spacing-20) 0;
    background: var(--white);
}

.disclaimer-content {
    max-width: 800px;
    margin: 0 auto;
}

.disclaimer-text {
    background: var(--gray-50);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    border-left: 4px solid var(--secondary-color);
}

.disclaimer-text p {
    margin-bottom: var(--spacing-4);
    color: var(--gray-700);
    line-height: 1.7;
}

.disclaimer-text p:last-child {
    margin-bottom: 0;
}

.disclaimer-text strong {
    color: var(--gray-900);
    font-weight: 600;
}

/* Copyright Section */
.copyright {
    padding: var(--spacing-16) 0;
    background: var(--gray-50);
}

.copyright-details {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
}

@media (min-width: 768px) {
    .copyright-details {
        grid-template-columns: repeat(2, 1fr);
    }
}

.copyright-item {
    background: var(--white);
    padding: var(--spacing-6);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.copyright-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.copyright-item h3 {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-4);
    color: var(--gray-900);
}

.copyright-item h3 i {
    color: var(--primary-color);
}

.copyright-item p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 0;
}

/* CTA Section */
.cta {
    padding: var(--spacing-20) 0;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    text-align: center;
}

.cta-content {
    max-width: 600px;
    margin: 0 auto;
}

.cta-title {
    font-size: var(--font-size-4xl);
    color: var(--white);
    margin-bottom: var(--spacing-4);
}

.cta-subtitle {
    font-size: var(--font-size-lg);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--spacing-8);
}

.cta .btn-primary {
    background-color: var(--white);
    color: var(--primary-color);
}

.cta .btn-primary:hover {
    background-color: var(--gray-100);
    color: var(--primary-dark);
}
