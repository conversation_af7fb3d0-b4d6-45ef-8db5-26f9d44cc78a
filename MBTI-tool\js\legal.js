// Legal pages (Privacy Policy & Terms of Service) JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeTableOfContents();
    initializeScrollProgress();
    initializeSmoothScrolling();
    initializeReadingTime();
});

// Initialize Table of Contents functionality
function initializeTableOfContents() {
    const tocLinks = document.querySelectorAll('.toc a');
    const sections = document.querySelectorAll('.legal-text section[id]');
    
    if (tocLinks.length === 0 || sections.length === 0) return;
    
    // Add click handlers for TOC links
    tocLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetSection = document.getElementById(targetId);
            
            if (targetSection) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetSection.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
                
                // Update active state
                updateActiveTocLink(this);
            }
        });
    });
    
    // Update active TOC link on scroll
    window.addEventListener('scroll', Utils.throttle(updateTocOnScroll, 100));
}

// Update active TOC link based on scroll position
function updateTocOnScroll() {
    const sections = document.querySelectorAll('.legal-text section[id]');
    const tocLinks = document.querySelectorAll('.toc a');
    const headerHeight = document.querySelector('.header').offsetHeight;
    const scrollPosition = window.scrollY + headerHeight + 100;
    
    let activeSection = null;
    
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionBottom = sectionTop + section.offsetHeight;
        
        if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
            activeSection = section;
        }
    });
    
    // Update active link
    tocLinks.forEach(link => {
        link.classList.remove('active');
        if (activeSection && link.getAttribute('href') === `#${activeSection.id}`) {
            link.classList.add('active');
        }
    });
}

// Update active TOC link
function updateActiveTocLink(activeLink) {
    const tocLinks = document.querySelectorAll('.toc a');
    tocLinks.forEach(link => link.classList.remove('active'));
    activeLink.classList.add('active');
}

// Initialize scroll progress indicator
function initializeScrollProgress() {
    // Create scroll progress indicator
    const scrollIndicator = document.createElement('div');
    scrollIndicator.className = 'scroll-indicator';
    scrollIndicator.innerHTML = '<div class="scroll-progress"></div>';
    document.body.appendChild(scrollIndicator);
    
    const scrollProgress = scrollIndicator.querySelector('.scroll-progress');
    
    // Update scroll progress
    window.addEventListener('scroll', Utils.throttle(() => {
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        
        scrollProgress.style.width = Math.min(scrollPercent, 100) + '%';
    }, 10));
}

// Initialize smooth scrolling for all anchor links
function initializeSmoothScrolling() {
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href === '#') return;
            
            e.preventDefault();
            const targetId = href.substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetElement.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
                
                // Update URL without triggering scroll
                if (history.pushState) {
                    history.pushState(null, null, href);
                }
            }
        });
    });
}

// Initialize reading time estimation
function initializeReadingTime() {
    const legalText = document.querySelector('.legal-text');
    if (!legalText) return;
    
    const text = legalText.textContent || legalText.innerText;
    const wordsPerMinute = 200; // Average reading speed
    const wordCount = text.trim().split(/\s+/).length;
    const readingTime = Math.ceil(wordCount / wordsPerMinute);
    
    // Add reading time to hero section
    const heroContent = document.querySelector('.hero-content');
    if (heroContent) {
        const readingTimeElement = document.createElement('div');
        readingTimeElement.className = 'reading-time';
        readingTimeElement.innerHTML = `
            <i class="fas fa-clock"></i>
            Estimated reading time: ${readingTime} minute${readingTime !== 1 ? 's' : ''}
        `;
        
        const lastUpdated = heroContent.querySelector('.last-updated');
        if (lastUpdated) {
            lastUpdated.parentNode.insertBefore(readingTimeElement, lastUpdated.nextSibling);
        } else {
            heroContent.appendChild(readingTimeElement);
        }
    }
}

// Print functionality
function initializePrintFeature() {
    // Add print button
    const printButton = document.createElement('button');
    printButton.className = 'print-button';
    printButton.innerHTML = '<i class="fas fa-print"></i> Print';
    printButton.addEventListener('click', () => window.print());
    
    const heroContent = document.querySelector('.hero-content');
    if (heroContent) {
        heroContent.appendChild(printButton);
    }
}

// Copy section link functionality
function initializeCopyLinks() {
    const headings = document.querySelectorAll('.legal-text h2[id]');
    
    headings.forEach(heading => {
        const copyButton = document.createElement('button');
        copyButton.className = 'copy-link-button';
        copyButton.innerHTML = '<i class="fas fa-link"></i>';
        copyButton.title = 'Copy link to this section';
        
        copyButton.addEventListener('click', function() {
            const url = window.location.origin + window.location.pathname + '#' + heading.id;
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(url).then(() => {
                    showCopyFeedback(this);
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = url;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showCopyFeedback(this);
            }
        });
        
        heading.appendChild(copyButton);
    });
}

// Show copy feedback
function showCopyFeedback(button) {
    const originalHTML = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i>';
    button.style.color = 'var(--accent-color)';
    
    setTimeout(() => {
        button.innerHTML = originalHTML;
        button.style.color = '';
    }, 2000);
}

// Search functionality within legal text
function initializeSearch() {
    const searchContainer = document.createElement('div');
    searchContainer.className = 'legal-search';
    searchContainer.innerHTML = `
        <div class="search-input-container">
            <input type="text" id="legalSearch" placeholder="Search within this document..." class="search-input">
            <button class="search-clear" id="searchClear">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="search-results" id="searchResults"></div>
    `;
    
    // const contentWrapper = document.querySelector('.content-wrapper');
    if (contentWrapper) {
        contentWrapper.insertBefore(searchContainer, contentWrapper.firstChild);
    }
    
    const searchInput = document.getElementById('legalSearch');
    const searchClear = document.getElementById('searchClear');
    const searchResults = document.getElementById('searchResults');
    
    if (searchInput) {
        searchInput.addEventListener('input', Utils.debounce(performSearch, 300));
        searchClear.addEventListener('click', clearSearch);
    }
    
    function performSearch() {
        const query = searchInput.value.trim().toLowerCase();
        if (query.length < 2) {
            clearSearch();
            return;
        }
        
        const legalText = document.querySelector('.legal-text');
        const sections = legalText.querySelectorAll('section[id]');
        const results = [];
        
        sections.forEach(section => {
            const sectionTitle = section.querySelector('h2').textContent;
            const sectionText = section.textContent.toLowerCase();
            
            if (sectionText.includes(query)) {
                const context = extractContext(section.textContent, query, 100);
                results.push({
                    id: section.id,
                    title: sectionTitle,
                    context: context
                });
            }
        });
        
        displaySearchResults(results, query);
    }
    
    function extractContext(text, query, contextLength) {
        const lowerText = text.toLowerCase();
        const queryIndex = lowerText.indexOf(query.toLowerCase());
        
        if (queryIndex === -1) return '';
        
        const start = Math.max(0, queryIndex - contextLength / 2);
        const end = Math.min(text.length, queryIndex + query.length + contextLength / 2);
        
        let context = text.substring(start, end);
        if (start > 0) context = '...' + context;
        if (end < text.length) context = context + '...';
        
        return context;
    }
    
    function displaySearchResults(results, query) {
        if (results.length === 0) {
            searchResults.innerHTML = '<p class="no-results">No results found.</p>';
            searchResults.style.display = 'block';
            return;
        }
        
        const resultsHTML = results.map(result => `
            <div class="search-result" data-section="${result.id}">
                <h4>${result.title}</h4>
                <p>${highlightQuery(result.context, query)}</p>
            </div>
        `).join('');
        
        searchResults.innerHTML = resultsHTML;
        searchResults.style.display = 'block';
        
        // Add click handlers for results
        searchResults.querySelectorAll('.search-result').forEach(result => {
            result.addEventListener('click', function() {
                const sectionId = this.dataset.section;
                const section = document.getElementById(sectionId);
                if (section) {
                    const headerHeight = document.querySelector('.header').offsetHeight;
                    const targetPosition = section.offsetTop - headerHeight - 20;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }
    
    function highlightQuery(text, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }
    
    function clearSearch() {
        searchInput.value = '';
        searchResults.style.display = 'none';
        searchResults.innerHTML = '';
    }
}

// Initialize all features
document.addEventListener('DOMContentLoaded', function() {
    initializePrintFeature();
    initializeCopyLinks();
    initializeSearch();
});

// Add CSS for additional features
const legalStyles = `
<style>
.reading-time {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-full);
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-4);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.print-button {
    background: var(--white);
    color: var(--primary-color);
    border: none;
    padding: var(--spacing-3) var(--spacing-6);
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    margin-top: var(--spacing-6);
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
}

.print-button:hover {
    background: var(--gray-100);
    transform: translateY(-1px);
}

.copy-link-button {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-2);
    margin-left: var(--spacing-4);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    opacity: 0;
}

.legal-text h2:hover .copy-link-button {
    opacity: 1;
}

.copy-link-button:hover {
    color: var(--primary-color);
    background: var(--gray-100);
}

.legal-search {
    background: var(--gray-50);
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    margin-bottom: var(--spacing-8);
    border: 1px solid var(--gray-200);
}

.search-input-container {
    position: relative;
    margin-bottom: var(--spacing-4);
}

.search-input {
    width: 100%;
    padding: var(--spacing-4) var(--spacing-12) var(--spacing-4) var(--spacing-4);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    transition: border-color var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.search-clear {
    position: absolute;
    right: var(--spacing-4);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--radius-md);
}

.search-clear:hover {
    color: var(--gray-600);
}

.search-results {
    display: none;
    max-height: 300px;
    overflow-y: auto;
}

.search-result {
    background: var(--white);
    padding: var(--spacing-4);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-3);
    cursor: pointer;
    transition: all var(--transition-fast);
    border: 1px solid var(--gray-200);
}

.search-result:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.search-result h4 {
    margin: 0 0 var(--spacing-2) 0;
    color: var(--primary-color);
    font-size: var(--font-size-base);
}

.search-result p {
    margin: 0;
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

.search-result mark {
    background: var(--secondary-color);
    color: var(--white);
    padding: 1px 3px;
    border-radius: 2px;
}

.no-results {
    text-align: center;
    color: var(--gray-500);
    font-style: italic;
    padding: var(--spacing-8);
}

@media (max-width: 768px) {
    .legal-search {
        padding: var(--spacing-4);
    }
    
    .copy-link-button {
        opacity: 1;
        position: static;
        margin-left: var(--spacing-2);
    }
}
</style>
`;

// Add styles to head
document.head.insertAdjacentHTML('beforeend', legalStyles);
