// Index page specific JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeHeroAnimations();
    initializePersonalityWheel();
    initializeTypeCards();
    initializeCounterAnimations();
    initializeSmoothScrolling();
});

// Hero section animations
function initializeHeroAnimations() {
    const heroContent = document.querySelector('.hero-content');
    const heroVisual = document.querySelector('.hero-visual');
    
    if (heroContent) {
        // Animate hero content on load
        setTimeout(() => {
            heroContent.style.opacity = '1';
            heroContent.style.transform = 'translateY(0)';
        }, 300);
    }
    
    if (heroVisual) {
        // Animate hero visual on load
        setTimeout(() => {
            heroVisual.style.opacity = '1';
            heroVisual.style.transform = 'translateY(0)';
        }, 600);
    }
}

// Personality wheel interactions
function initializePersonalityWheel() {
    const segments = document.querySelectorAll('.segment');
    const wheelCenter = document.querySelector('.wheel-center');
    
    segments.forEach(segment => {
        segment.addEventListener('mouseenter', function() {
            const type = this.dataset.type;
            if (wheelCenter) {
                wheelCenter.innerHTML = `<i class="fas fa-user-circle"></i><span class="type-preview">${type}</span>`;
            }
            
            // Highlight related segments
            segments.forEach(s => {
                if (s !== this) {
                    s.style.opacity = '0.5';
                }
            });
        });
        
        segment.addEventListener('mouseleave', function() {
            if (wheelCenter) {
                wheelCenter.innerHTML = '<i class="fas fa-user-circle"></i>';
            }
            
            // Reset all segments
            segments.forEach(s => {
                s.style.opacity = '1';
            });
        });
        
        segment.addEventListener('click', function() {
            const type = this.dataset.type;
            showPersonalityInfo(type);
        });
    });
}

// Show personality type information
function showPersonalityInfo(type) {
    const personalityInfo = {
        'INTJ': {
            name: 'The Architect',
            description: 'Imaginative and strategic thinkers, with a plan for everything.',
            traits: ['Strategic', 'Independent', 'Decisive', 'Hardworking']
        },
        'INTP': {
            name: 'The Thinker',
            description: 'Innovative inventors with an unquenchable thirst for knowledge.',
            traits: ['Logical', 'Creative', 'Objective', 'Curious']
        },
        'ENTJ': {
            name: 'The Commander',
            description: 'Bold, imaginative and strong-willed leaders.',
            traits: ['Efficient', 'Energetic', 'Self-confident', 'Strong-willed']
        },
        'ENTP': {
            name: 'The Debater',
            description: 'Smart and curious thinkers who cannot resist an intellectual challenge.',
            traits: ['Quick', 'Ingenious', 'Stimulating', 'Alert']
        },
        'INFJ': {
            name: 'The Advocate',
            description: 'Quiet and mystical, yet very inspiring and tireless idealists.',
            traits: ['Creative', 'Insightful', 'Inspiring', 'Decisive']
        },
        'INFP': {
            name: 'The Mediator',
            description: 'Poetic, kind and altruistic people, always eager to help a good cause.',
            traits: ['Idealistic', 'Loyal', 'Adaptable', 'Curious']
        },
        'ENFJ': {
            name: 'The Protagonist',
            description: 'Charismatic and inspiring leaders, able to mesmerize their listeners.',
            traits: ['Tolerant', 'Reliable', 'Charismatic', 'Altruistic']
        },
        'ENFP': {
            name: 'The Campaigner',
            description: 'Enthusiastic, creative and sociable free spirits.',
            traits: ['Enthusiastic', 'Creative', 'Sociable', 'Energetic']
        },
        'ISTJ': {
            name: 'The Logistician',
            description: 'Practical and fact-minded, reliable and responsible.',
            traits: ['Honest', 'Direct', 'Strong-willed', 'Dutiful']
        },
        'ISFJ': {
            name: 'The Protector',
            description: 'Very dedicated and warm protectors, always ready to defend their loved ones.',
            traits: ['Supportive', 'Reliable', 'Patient', 'Imaginative']
        },
        'ESTJ': {
            name: 'The Executive',
            description: 'Excellent administrators, unsurpassed at managing things or people.',
            traits: ['Dedicated', 'Strong-willed', 'Direct', 'Honest']
        },
        'ESFJ': {
            name: 'The Consul',
            description: 'Extraordinarily caring, social and popular people, always eager to help.',
            traits: ['Strong practical skills', 'Loyal', 'Sensitive', 'Warm-hearted']
        },
        'ISTP': {
            name: 'The Virtuoso',
            description: 'Bold and practical experimenters, masters of all kinds of tools.',
            traits: ['Tolerant', 'Flexible', 'Charming', 'Unpredictable']
        },
        'ISFP': {
            name: 'The Adventurer',
            description: 'Flexible and charming artists, always ready to explore new possibilities.',
            traits: ['Charming', 'Sensitive', 'Imaginative', 'Passionate']
        },
        'ESTP': {
            name: 'The Entrepreneur',
            description: 'Smart, energetic and very perceptive people, truly enjoy living on the edge.',
            traits: ['Bold', 'Rational', 'Practical', 'Original']
        },
        'ESFP': {
            name: 'The Entertainer',
            description: 'Spontaneous, energetic and enthusiastic people – life is never boring.',
            traits: ['Bold', 'Original', 'Aesthetics', 'Showmanship']
        }
    };
    
    const info = personalityInfo[type];
    if (info) {
        // Create modal or tooltip to show personality info
        showPersonalityModal(type, info);
    }
}

// Show personality modal
function showPersonalityModal(type, info) {
    // Remove existing modal if any
    const existingModal = document.querySelector('.personality-modal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Create modal
    const modal = document.createElement('div');
    modal.className = 'personality-modal';
    modal.innerHTML = `
        <div class="modal-overlay">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${type} - ${info.name}</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <p class="personality-description">${info.description}</p>
                    <div class="personality-traits">
                        <h4>Key Traits:</h4>
                        <ul>
                            ${info.traits.map(trait => `<li>${trait}</li>`).join('')}
                        </ul>
                    </div>
                    <div class="modal-actions">
                        <a href="test.html" class="btn btn-primary">Take Test</a>
                        <button class="btn btn-secondary modal-close">Close</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Add event listeners
    const closeButtons = modal.querySelectorAll('.modal-close');
    const overlay = modal.querySelector('.modal-overlay');
    
    closeButtons.forEach(button => {
        button.addEventListener('click', () => modal.remove());
    });
    
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            modal.remove();
        }
    });
    
    // Animate modal in
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

// Type cards interactions
function initializeTypeCards() {
    const typeCards = document.querySelectorAll('.type-card');
    
    typeCards.forEach(card => {
        card.addEventListener('click', function() {
            const type = this.dataset.type;
            if (type) {
                showPersonalityInfo(type);
            }
        });
        
        // Add hover effect
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// Counter animations for stats
function initializeCounterAnimations() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    const animateCounter = (element, target, duration = 2000) => {
        const start = 0;
        const increment = target / (duration / 16);
        let current = start;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            // Format number based on target
            let displayValue;
            if (target >= 1000000) {
                displayValue = (current / 1000000).toFixed(1) + 'M+';
            } else if (target >= 1000) {
                displayValue = (current / 1000).toFixed(0) + 'K+';
            } else if (target < 100) {
                displayValue = Math.floor(current);
            } else {
                displayValue = Math.floor(current) + '%';
            }
            
            element.textContent = displayValue;
        }, 16);
    };
    
    // Intersection Observer for counter animation
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting && !entry.target.classList.contains('animated')) {
                const text = entry.target.textContent;
                let target;
                
                if (text.includes('M+')) {
                    target = parseFloat(text) * 1000000;
                } else if (text.includes('K+')) {
                    target = parseFloat(text) * 1000;
                } else if (text.includes('%')) {
                    target = parseInt(text);
                } else {
                    target = parseInt(text);
                }
                
                entry.target.classList.add('animated');
                animateCounter(entry.target, target);
            }
        });
    }, { threshold: 0.5 });
    
    statNumbers.forEach(stat => {
        counterObserver.observe(stat);
    });
}

// Smooth scrolling for anchor links
function initializeSmoothScrolling() {
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetElement.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Parallax effect for hero section
function initializeParallaxEffect() {
    const hero = document.querySelector('.hero');
    const heroContent = document.querySelector('.hero-content');
    
    if (hero && heroContent) {
        window.addEventListener('scroll', Utils.throttle(() => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            
            heroContent.style.transform = `translateY(${rate}px)`;
        }, 10));
    }
}

// Initialize parallax on load
window.addEventListener('load', initializeParallaxEffect);

// Add CSS for modal
const modalStyles = `
<style>
.personality-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.personality-modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.modal-content {
    background: white;
    border-radius: 12px;
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    transform: translateY(20px);
    transition: transform 0.3s ease;
}

.personality-modal.show .modal-content {
    transform: translateY(0);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
    margin: 0;
    color: var(--primary-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 20px;
}

.personality-description {
    font-size: 16px;
    margin-bottom: 20px;
    color: #374151;
}

.personality-traits h4 {
    margin-bottom: 10px;
    color: #1f2937;
}

.personality-traits ul {
    list-style: none;
    padding: 0;
}

.personality-traits li {
    padding: 5px 0;
    color: #6b7280;
    position: relative;
    padding-left: 20px;
}

.personality-traits li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-weight: bold;
}

.modal-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    justify-content: flex-end;
}
</style>
`;

// Add modal styles to head
document.head.insertAdjacentHTML('beforeend', modalStyles);
