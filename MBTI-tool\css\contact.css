/* Contact Page Specific Styles */

/* Contact Hero Section */
.contact-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.contact-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="contact-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23contact-pattern)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 600px;
    margin: 0 auto;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    color: var(--white);
    margin-bottom: var(--spacing-6);
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin-bottom: 0;
}

/* Contact Section */
.contact-section {
    padding: var(--spacing-20) 0;
    background: var(--white);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-12);
}

@media (min-width: 1024px) {
    .contact-content {
        grid-template-columns: 1fr 1fr;
    }
}

/* Contact Info */
.contact-info {
    padding: var(--spacing-8);
}

.contact-description {
    font-size: var(--font-size-lg);
    color: var(--gray-700);
    margin-bottom: var(--spacing-8);
    line-height: 1.7;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-4);
    padding: var(--spacing-6);
    background: var(--gray-50);
    border-radius: var(--radius-xl);
    transition: all var(--transition-normal);
    border: 2px solid transparent;
}

.contact-method:hover {
    background: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.method-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.method-info h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-2);
    color: var(--gray-900);
}

.method-info p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-2);
}

.contact-link {
    color: var(--primary-color);
    font-weight: 500;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.contact-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.contact-detail {
    color: var(--gray-500);
    font-size: var(--font-size-sm);
}

/* Contact Form */
.contact-form-container {
    background: var(--gray-50);
    padding: var(--spacing-8);
    border-radius: var(--radius-2xl);
    border: 1px solid var(--gray-200);
}

.form-title {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-8);
    color: var(--gray-900);
    text-align: center;
}

.form-group {
    margin-bottom: var(--spacing-6);
}

.form-label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-2);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-4);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-family: inherit;
    transition: all var(--transition-fast);
    background: var(--white);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
    cursor: pointer;
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: var(--white);
    font-size: var(--font-size-sm);
    font-weight: bold;
}

.form-submit {
    width: 100%;
    margin-top: var(--spacing-4);
}

.form-status {
    margin-top: var(--spacing-4);
    padding: var(--spacing-4);
    border-radius: var(--radius-lg);
    text-align: center;
    font-weight: 500;
    display: none;
}

.form-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.form-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* FAQ Section */
.faq-section {
    padding: var(--spacing-20) 0;
    background: var(--gray-50);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-16);
}

.section-title {
    margin-bottom: var(--spacing-4);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

.faq-list {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: var(--white);
    border-radius: var(--radius-xl);
    margin-bottom: var(--spacing-4);
    overflow: hidden;
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.faq-item:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.faq-question {
    padding: var(--spacing-6);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all var(--transition-fast);
}

.faq-question:hover {
    background: var(--gray-50);
}

.faq-question h3 {
    font-size: var(--font-size-lg);
    color: var(--gray-900);
    margin: 0;
}

.faq-question i {
    color: var(--primary-color);
    transition: transform var(--transition-fast);
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 var(--spacing-6) var(--spacing-6);
    max-height: 0;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.faq-item.active .faq-answer {
    max-height: 200px;
    padding-top: 0;
}

.faq-answer p {
    color: var(--gray-700);
    line-height: 1.6;
    margin-bottom: 0;
}

.faq-answer a {
    color: var(--primary-color);
    text-decoration: none;
}

.faq-answer a:hover {
    text-decoration: underline;
}

/* CTA Section */
.cta {
    padding: var(--spacing-20) 0;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    text-align: center;
}

.cta-content {
    max-width: 600px;
    margin: 0 auto;
}

.cta-title {
    font-size: var(--font-size-4xl);
    color: var(--white);
    margin-bottom: var(--spacing-4);
}

.cta-subtitle {
    font-size: var(--font-size-lg);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--spacing-8);
}

.cta .btn-primary {
    background-color: var(--white);
    color: var(--primary-color);
}

.cta .btn-primary:hover {
    background-color: var(--gray-100);
    color: var(--primary-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .contact-method {
        flex-direction: column;
        text-align: center;
    }
    
    .method-icon {
        margin: 0 auto var(--spacing-4);
    }
    
    .contact-form-container {
        padding: var(--spacing-6);
    }
    
    .checkbox-label {
        align-items: flex-start;
    }
}

/* Loading state for form */
.form-submit.loading {
    position: relative;
    color: transparent;
}

.form-submit.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}
