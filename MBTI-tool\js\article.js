// Article page specific JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeTableOfContents();
    initializeScrollProgress();
    initializeReadingProgress();
    initializeArticleAnimations();
    initializeSocialSharing();
    initializeReadingTime();
});

// Initialize Table of Contents functionality
function initializeTableOfContents() {
    const tocLinks = document.querySelectorAll('.toc-list a');
    const sections = document.querySelectorAll('.article-body section[id]');
    
    if (tocLinks.length === 0 || sections.length === 0) return;
    
    // Add click handlers for TOC links
    tocLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetSection = document.getElementById(targetId);
            
            if (targetSection) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetSection.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
                
                // Update active state
                updateActiveTocLink(this);
            }
        });
    });
    
    // Update active TOC link on scroll
    window.addEventListener('scroll', Utils.throttle(updateTocOnScroll, 100));
}

// Update active TOC link based on scroll position
function updateTocOnScroll() {
    const sections = document.querySelectorAll('.article-body section[id]');
    const tocLinks = document.querySelectorAll('.toc-list a');
    const headerHeight = document.querySelector('.header').offsetHeight;
    const scrollPosition = window.scrollY + headerHeight + 100;
    
    let activeSection = null;
    
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionBottom = sectionTop + section.offsetHeight;
        
        if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
            activeSection = section;
        }
    });
    
    // Update active link
    tocLinks.forEach(link => {
        link.classList.remove('active');
        if (activeSection && link.getAttribute('href') === `#${activeSection.id}`) {
            link.classList.add('active');
        }
    });
}

// Update active TOC link
function updateActiveTocLink(activeLink) {
    const tocLinks = document.querySelectorAll('.toc-list a');
    tocLinks.forEach(link => link.classList.remove('active'));
    activeLink.classList.add('active');
}

// Initialize scroll progress indicator
function initializeScrollProgress() {
    // Create scroll progress indicator
    const scrollIndicator = document.createElement('div');
    scrollIndicator.className = 'scroll-indicator';
    scrollIndicator.innerHTML = '<div class="scroll-progress"></div>';
    document.body.appendChild(scrollIndicator);
    
    const scrollProgress = scrollIndicator.querySelector('.scroll-progress');
    
    // Update scroll progress
    window.addEventListener('scroll', Utils.throttle(() => {
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        
        scrollProgress.style.width = Math.min(scrollPercent, 100) + '%';
    }, 10));
}

// Initialize reading progress
function initializeReadingProgress() {
    const articleBody = document.querySelector('.article-body');
    if (!articleBody) return;
    
    // Create reading progress indicator
    const readingProgress = document.createElement('div');
    readingProgress.className = 'reading-progress';
    readingProgress.innerHTML = `
        <div class="reading-stats">
            <span class="reading-time-left">Estimated reading time: <span id="timeLeft">0</span> min</span>
            <span class="reading-percentage"><span id="readingPercent">0</span>% complete</span>
        </div>
    `;
    
    // Insert after article header
    const articleHeader = document.querySelector('.article-header');
    if (articleHeader) {
        articleHeader.parentNode.insertBefore(readingProgress, articleHeader.nextSibling);
    }
    
    // Calculate reading progress
    window.addEventListener('scroll', Utils.throttle(updateReadingProgress, 100));
}

// Update reading progress
function updateReadingProgress() {
    const articleBody = document.querySelector('.article-body');
    const timeLeftElement = document.getElementById('timeLeft');
    const readingPercentElement = document.getElementById('readingPercent');
    
    if (!articleBody || !timeLeftElement || !readingPercentElement) return;
    
    const articleTop = articleBody.offsetTop;
    const articleHeight = articleBody.offsetHeight;
    const windowHeight = window.innerHeight;
    const scrollTop = window.pageYOffset;
    
    // Calculate how much of the article has been read
    const articleStart = articleTop - windowHeight / 2;
    const articleEnd = articleTop + articleHeight - windowHeight / 2;
    const readProgress = Math.max(0, Math.min(100, 
        ((scrollTop - articleStart) / (articleEnd - articleStart)) * 100
    ));
    
    // Update reading percentage
    readingPercentElement.textContent = Math.round(readProgress);
    
    // Calculate estimated time left
    const totalReadingTime = calculateReadingTime(articleBody);
    const timeLeft = Math.max(0, Math.round(totalReadingTime * (100 - readProgress) / 100));
    timeLeftElement.textContent = timeLeft;
}

// Calculate reading time
function calculateReadingTime(element) {
    const text = element.textContent || element.innerText;
    const wordsPerMinute = 200; // Average reading speed
    const wordCount = text.trim().split(/\s+/).length;
    return Math.ceil(wordCount / wordsPerMinute);
}

// Initialize article animations
function initializeArticleAnimations() {
    const animatedElements = document.querySelectorAll(
        '.dimension-card, .type-card, .tip-item, .related-article'
    );
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.classList.add('animate-in');
                }, index * 100);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    animatedElements.forEach(element => {
        element.classList.add('animate-on-scroll');
        observer.observe(element);
    });
}

// Initialize social sharing
function initializeSocialSharing() {
    // Create social sharing buttons
    const socialShare = document.createElement('div');
    socialShare.className = 'social-share';
    socialShare.innerHTML = `
        <div class="share-buttons">
            <h4>Share this article</h4>
            <div class="share-button-group">
                <button class="share-btn twitter" data-platform="twitter">
                    <i class="fab fa-twitter"></i>
                    Twitter
                </button>
                <button class="share-btn facebook" data-platform="facebook">
                    <i class="fab fa-facebook"></i>
                    Facebook
                </button>
                <button class="share-btn linkedin" data-platform="linkedin">
                    <i class="fab fa-linkedin"></i>
                    LinkedIn
                </button>
                <button class="share-btn copy" data-platform="copy">
                    <i class="fas fa-link"></i>
                    Copy Link
                </button>
            </div>
        </div>
    `;
    
    // Insert at the end of article body
    const articleBody = document.querySelector('.article-body');
    if (articleBody) {
        articleBody.appendChild(socialShare);
    }
    
    // Add click handlers
    const shareButtons = socialShare.querySelectorAll('.share-btn');
    shareButtons.forEach(button => {
        button.addEventListener('click', function() {
            const platform = this.dataset.platform;
            handleSocialShare(platform);
        });
    });
}

// Handle social sharing
function handleSocialShare(platform) {
    const url = window.location.href;
    const title = document.querySelector('.article-title').textContent;
    const description = document.querySelector('.article-subtitle').textContent;
    
    let shareUrl = '';
    
    switch (platform) {
        case 'twitter':
            shareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`;
            break;
        case 'facebook':
            shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
            break;
        case 'linkedin':
            shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
            break;
        case 'copy':
            copyToClipboard(url);
            return;
    }
    
    if (shareUrl) {
        window.open(shareUrl, '_blank', 'width=600,height=400');
        trackSocialShare(platform);
    }
}

// Copy to clipboard
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showShareFeedback('Link copied to clipboard!');
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showShareFeedback('Link copied to clipboard!');
    }
    
    trackSocialShare('copy');
}

// Show share feedback
function showShareFeedback(message) {
    const feedback = document.createElement('div');
    feedback.className = 'share-feedback';
    feedback.textContent = message;
    feedback.style.cssText = `
        position: fixed;
        top: 100px;
        left: 50%;
        transform: translateX(-50%);
        background: var(--accent-color);
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        z-index: 1000;
        font-weight: 500;
        box-shadow: var(--shadow-lg);
    `;
    
    document.body.appendChild(feedback);
    
    setTimeout(() => {
        feedback.remove();
    }, 3000);
}

// Track social sharing
function trackSocialShare(platform) {
    const analytics = Utils.storage.get('social_analytics', {
        shares: 0,
        platforms: {}
    });
    
    analytics.shares++;
    analytics.platforms[platform] = (analytics.platforms[platform] || 0) + 1;
    
    Utils.storage.set('social_analytics', analytics);
    
    if (typeof gtag !== 'undefined') {
        gtag('event', 'share', {
            method: platform,
            content_type: 'article',
            item_id: window.location.pathname
        });
    }
}

// Initialize reading time display
function initializeReadingTime() {
    const articleBody = document.querySelector('.article-body');
    if (!articleBody) return;
    
    const readingTime = calculateReadingTime(articleBody);
    
    // Update reading time in article stats
    const readTimeElement = document.querySelector('.read-time');
    if (readTimeElement) {
        readTimeElement.innerHTML = `<i class="fas fa-clock"></i> ${readingTime} min read`;
    }
}

// Smooth scrolling for anchor links
function initializeSmoothScrolling() {
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href === '#') return;
            
            e.preventDefault();
            const targetId = href.substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetElement.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Initialize smooth scrolling on load
document.addEventListener('DOMContentLoaded', initializeSmoothScrolling);

// Add CSS for additional features
const articleStyles = `
<style>
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--gray-200);
    z-index: 9999;
}

.scroll-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    width: 0%;
    transition: width 0.1s ease;
}

.reading-progress {
    background: var(--white);
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--gray-200);
    position: sticky;
    top: 70px;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.reading-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.social-share {
    margin-top: var(--spacing-12);
    padding-top: var(--spacing-8);
    border-top: 2px solid var(--gray-200);
}

.share-buttons h4 {
    margin-bottom: var(--spacing-6);
    color: var(--gray-900);
    text-align: center;
}

.share-button-group {
    display: flex;
    justify-content: center;
    gap: var(--spacing-4);
    flex-wrap: wrap;
}

.share-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    font-size: var(--font-size-sm);
}

.share-btn.twitter {
    background: #1da1f2;
    color: white;
}

.share-btn.facebook {
    background: #4267b2;
    color: white;
}

.share-btn.linkedin {
    background: #0077b5;
    color: white;
}

.share-btn.copy {
    background: var(--gray-600);
    color: white;
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
}

@media (max-width: 768px) {
    .reading-stats {
        flex-direction: column;
        gap: var(--spacing-2);
        text-align: center;
    }
    
    .share-button-group {
        flex-direction: column;
        align-items: center;
    }
    
    .share-btn {
        width: 200px;
        justify-content: center;
    }
}
</style>
`;

// Add styles to head
document.head.insertAdjacentHTML('beforeend', articleStyles);
