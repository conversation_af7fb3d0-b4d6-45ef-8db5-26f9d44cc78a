/* Index Page Specific Styles */

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-12);
    align-items: center;
    position: relative;
    z-index: 1;
}

@media (min-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr 1fr;
    }
}

.hero-content {
    text-align: center;
}

@media (min-width: 1024px) {
    .hero-content {
        text-align: left;
    }
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--white);
    margin-bottom: var(--spacing-6);
    line-height: 1.1;
}

@media (min-width: 768px) {
    .hero-title {
        font-size: var(--font-size-5xl);
    }
}

.hero-subtitle {
    font-size: var(--font-size-lg);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--spacing-8);
    max-width: 600px;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-8);
    margin-bottom: var(--spacing-10);
    flex-wrap: wrap;
}

@media (min-width: 1024px) {
    .hero-stats {
        justify-content: flex-start;
    }
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--white);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.8);
}

.hero-actions {
    display: flex;
    gap: var(--spacing-4);
    justify-content: center;
    flex-wrap: wrap;
}

@media (min-width: 1024px) {
    .hero-actions {
        justify-content: flex-start;
    }
}

/* Personality Wheel */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.personality-wheel {
    position: relative;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    animation: rotate 60s linear infinite;
}

@media (max-width: 768px) {
    .personality-wheel {
        width: 300px;
        height: 300px;
    }
}

.wheel-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
    box-shadow: var(--shadow-lg);
}

.wheel-segments {
    position: relative;
    width: 100%;
    height: 100%;
}

.segment {
    position: absolute;
    width: 60px;
    height: 30px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xs);
    font-weight: 600;
    color: var(--gray-800);
    cursor: pointer;
    transition: all var(--transition-normal);
    animation: counter-rotate 60s linear infinite;
}

.segment:hover {
    background: var(--white);
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

/* Position segments around the circle */
.segment:nth-child(1) { top: 10px; left: 50%; transform: translateX(-50%); }
.segment:nth-child(2) { top: 25px; right: 25px; }
.segment:nth-child(3) { top: 60px; right: 10px; }
.segment:nth-child(4) { top: 100px; right: 5px; }
.segment:nth-child(5) { top: 140px; right: 10px; }
.segment:nth-child(6) { top: 175px; right: 25px; }
.segment:nth-child(7) { bottom: 25px; right: 25px; }
.segment:nth-child(8) { bottom: 10px; left: 50%; transform: translateX(-50%); }
.segment:nth-child(9) { bottom: 25px; left: 25px; }
.segment:nth-child(10) { top: 175px; left: 25px; }
.segment:nth-child(11) { top: 140px; left: 10px; }
.segment:nth-child(12) { top: 100px; left: 5px; }
.segment:nth-child(13) { top: 60px; left: 10px; }
.segment:nth-child(14) { top: 25px; left: 25px; }
.segment:nth-child(15) { top: 60px; right: 10px; }
.segment:nth-child(16) { top: 100px; right: 5px; }

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes counter-rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(-360deg); }
}

/* Features Section */
.features {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-16);
}

.section-title {
    margin-bottom: var(--spacing-4);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
}

@media (min-width: 768px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.feature-card {
    background: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-6);
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.feature-title {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-4);
    color: var(--gray-900);
}

.feature-description {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Personality Types Section */
.personality-types {
    padding: var(--spacing-20) 0;
    background: var(--white);
}

.types-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
}

@media (min-width: 768px) {
    .types-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .types-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-12);
    }
}

.type-category {
    background: var(--gray-50);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-8);
    transition: all var(--transition-normal);
    border: 2px solid transparent;
}

.type-category:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.type-category[data-category="analysts"] {
    background: linear-gradient(135deg, #f3e8ff, #e0e7ff);
}

.type-category[data-category="diplomats"] {
    background: linear-gradient(135deg, #ecfdf5, #f0fdf4);
}

.type-category[data-category="sentinels"] {
    background: linear-gradient(135deg, #fef3c7, #fef7cd);
}

.type-category[data-category="explorers"] {
    background: linear-gradient(135deg, #fee2e2, #fef2f2);
}

.category-header {
    text-align: center;
    margin-bottom: var(--spacing-6);
}

.category-icon {
    width: 50px;
    height: 50px;
    background: var(--white);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-4);
    font-size: var(--font-size-xl);
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.category-title {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-2);
    color: var(--gray-900);
}

.category-subtitle {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

.category-types {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-4);
}

.type-card {
    background: var(--white);
    padding: var(--spacing-4);
    border-radius: var(--radius-lg);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    border: 1px solid var(--gray-200);
}

.type-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.type-code {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-1);
}

.type-name {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

/* Benefits Section */
.benefits-section {
    padding: var(--spacing-20) 0;
    background: var(--white);
}

.benefits-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-12);
    align-items: center;
}

@media (min-width: 1024px) {
    .benefits-content {
        grid-template-columns: 1fr 1fr;
    }
}

.benefits-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-8);
}

.benefit-circle {
    position: relative;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-xl);
}

.circle-item {
    position: absolute;
    width: 80px;
    height: 80px;
    background: var(--white);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
    border: 3px solid transparent;
}

.circle-item:hover,
.circle-item.active {
    transform: scale(1.1);
    border-color: var(--accent-color);
    background: var(--accent-color);
    color: var(--white);
}

.circle-item i {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-1);
    color: var(--primary-color);
    transition: color var(--transition-fast);
}

.circle-item:hover i,
.circle-item.active i {
    color: var(--white);
}

.circle-item span {
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-align: center;
    line-height: 1.2;
}

/* Position circle items */
.circle-item:nth-child(1) { top: 10px; left: 50%; transform: translateX(-50%); }
.circle-item:nth-child(2) { top: 25%; right: 10px; }
.circle-item:nth-child(3) { bottom: 25%; right: 10px; }
.circle-item:nth-child(4) { bottom: 10px; left: 50%; transform: translateX(-50%); }
.circle-item:nth-child(5) { bottom: 25%; left: 10px; }
.circle-item:nth-child(6) { top: 25%; left: 10px; }

.benefits-details {
    padding: var(--spacing-8);
}

.benefit-detail {
    display: none;
    opacity: 0;
    transform: translateY(20px);
    transition: all var(--transition-normal);
}

.benefit-detail.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.benefit-detail h3 {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    font-size: var(--font-size-xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-4);
}

.benefit-detail p {
    color: var(--gray-700);
    line-height: 1.7;
    margin-bottom: var(--spacing-6);
    font-size: var(--font-size-base);
}

.benefit-detail ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.benefit-detail li {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-3);
    color: var(--gray-600);
    line-height: 1.6;
}

.benefit-detail li::before {
    content: '✓';
    color: var(--accent-color);
    font-weight: bold;
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

/* How It Works Section */
.how-it-works {
    padding: var(--spacing-20) 0;
    background: var(--gray-50);
}

.process-timeline {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
}

.process-timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--primary-color);
    transform: translateX(-50%);
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-16);
    position: relative;
}

.timeline-item.reverse {
    flex-direction: row-reverse;
}

.timeline-number {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-xl);
    font-weight: 700;
    z-index: 2;
    box-shadow: var(--shadow-lg);
}

.timeline-content {
    flex: 1;
    background: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    margin: 0 var(--spacing-8);
    border: 2px solid transparent;
    transition: all var(--transition-normal);
}

.timeline-item:hover .timeline-content {
    border-color: var(--primary-color);
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.timeline-content h3 {
    font-size: var(--font-size-xl);
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.timeline-content p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-6);
}

.timeline-features {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-2);
}

.feature-tag {
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.timeline-visual {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 var(--spacing-8);
}

.timeline-visual i {
    font-size: var(--font-size-5xl);
    color: var(--primary-color);
    opacity: 0.3;
}

/* Testimonials Section */
.testimonials {
    padding: var(--spacing-20) 0;
    background: var(--white);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
}

@media (min-width: 768px) {
    .testimonials-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .testimonials-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.testimonial-card {
    background: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.testimonial-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.testimonial-content {
    margin-bottom: var(--spacing-6);
}

.testimonial-content p {
    font-style: italic;
    color: var(--gray-700);
    margin-bottom: 0;
    position: relative;
}

.testimonial-content p::before {
    content: '"';
    font-size: var(--font-size-4xl);
    color: var(--primary-color);
    position: absolute;
    top: -10px;
    left: -10px;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: var(--radius-full);
    overflow: hidden;
    flex-shrink: 0;
}

.author-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.author-name {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.author-title {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-bottom: var(--spacing-1);
}

.author-type {
    font-size: var(--font-size-xs);
    color: var(--primary-color);
    font-weight: 500;
}

/* CTA Section */
.cta {
    padding: var(--spacing-20) 0;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    text-align: center;
}

.cta-content {
    max-width: 600px;
    margin: 0 auto;
}

.cta-title {
    font-size: var(--font-size-4xl);
    color: var(--white);
    margin-bottom: var(--spacing-4);
}

.cta-subtitle {
    font-size: var(--font-size-lg);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--spacing-8);
}

.cta .btn-primary {
    background-color: var(--white);
    color: var(--primary-color);
}

.cta .btn-primary:hover {
    background-color: var(--gray-100);
    color: var(--primary-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: var(--font-size-4xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .personality-wheel {
        width: 300px;
        height: 300px;
    }

    .type-card {
        width: 80px;
        height: 80px;
    }

    .type-card span {
        font-size: var(--font-size-xs);
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .testimonial-card {
        margin: 0 var(--spacing-4);
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    /* Benefits section mobile */
    .benefit-circle {
        width: 300px;
        height: 300px;
    }

    .circle-item {
        width: 60px;
        height: 60px;
    }

    .circle-item i {
        font-size: var(--font-size-base);
    }

    .circle-item span {
        font-size: 10px;
    }

    .benefits-details {
        padding: var(--spacing-4);
    }

    /* Timeline mobile */
    .process-timeline::before {
        left: 30px;
    }

    .timeline-item,
    .timeline-item.reverse {
        flex-direction: column;
        align-items: flex-start;
        padding-left: 60px;
    }

    .timeline-number {
        left: 30px;
        transform: translateX(-50%);
    }

    .timeline-content {
        margin: var(--spacing-4) 0 0 0;
        width: 100%;
    }

    .timeline-visual {
        display: none;
    }

    .timeline-features {
        justify-content: flex-start;
    }
}
