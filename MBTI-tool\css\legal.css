/* Legal Pages (Privacy Policy & Terms of Service) Styles */

/* Legal Hero Section */
.legal-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.legal-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="legal-pattern" width="30" height="30" patternUnits="userSpaceOnUse"><rect width="2" height="2" x="14" y="14" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23legal-pattern)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 700px;
    margin: 0 auto;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    color: var(--white);
    margin-bottom: var(--spacing-6);
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin-bottom: var(--spacing-8);
}

.last-updated {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-3) var(--spacing-6);
    border-radius: var(--radius-full);
    color: rgba(255, 255, 255, 0.9);
    font-size: var(--font-size-sm);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Legal Content Section */
.legal-content {
    padding: var(--spacing-20) 0;
    background: var(--white);
}

.content-wrapper {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-12);
    max-width: 1200px;
    margin: 0 auto;
}

@media (min-width: 1024px) {
    .content-wrapper {
        grid-template-columns:1fr 300px;
    }
}

/* Table of Contents */
.toc {
    background: var(--gray-50);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    border: 1px solid var(--gray-200);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.toc h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-6);
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.toc h3::before {
    content: '📋';
    font-size: var(--font-size-base);
}

.toc ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.toc li {
    margin-bottom: var(--spacing-2);
}

.toc a {
    display: block;
    padding: var(--spacing-2) var(--spacing-3);
    color: var(--gray-600);
    text-decoration: none;
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
    border-left: 3px solid transparent;
}

.toc a:hover {
    background: var(--white);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
    transform: translateX(4px);
}

.toc a.active {
    background: var(--primary-color);
    color: var(--white);
    border-left-color: var(--primary-dark);
}

/* Legal Text Content */
.legal-text {
    background: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    border: 1px solid var(--gray-200);
    box-shadow: var(--shadow-sm);
}

.legal-text section {
    margin-bottom: var(--spacing-12);
    padding-bottom: var(--spacing-8);
    border-bottom: 1px solid var(--gray-200);
}

.legal-text section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.legal-text h2 {
    font-size: var(--font-size-2xl);
    color: var(--gray-900);
    margin-bottom: var(--spacing-6);
    padding-bottom: var(--spacing-3);
    border-bottom: 2px solid var(--primary-color);
    position: relative;
}

.legal-text h2::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: var(--primary-dark);
}

.legal-text h3 {
    font-size: var(--font-size-xl);
    color: var(--gray-800);
    margin: var(--spacing-8) 0 var(--spacing-4);
}

.legal-text h3:first-child {
    margin-top: 0;
}

.legal-text p {
    color: var(--gray-700);
    line-height: 1.7;
    margin-bottom: var(--spacing-4);
}

.legal-text ul, .legal-text ol {
    margin: var(--spacing-4) 0;
    padding-left: var(--spacing-6);
}

.legal-text li {
    color: var(--gray-700);
    line-height: 1.6;
    margin-bottom: var(--spacing-2);
}

.legal-text li strong {
    color: var(--gray-900);
}

.legal-text a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-fast);
}

.legal-text a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Highlight Boxes */
.highlight-box {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    margin: var(--spacing-6) 0;
    position: relative;
    overflow: hidden;
}

.highlight-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="highlight-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23highlight-pattern)"/></svg>');
    opacity: 0.3;
}

.highlight-box * {
    position: relative;
    z-index: 1;
}

.highlight-box h3 {
    color: var(--white);
    margin-top: 0;
    margin-bottom: var(--spacing-4);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.highlight-box p {
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 0;
}

.highlight-box.warning {
    background: linear-gradient(135deg, var(--secondary-color), #f59e0b);
}

/* Contact Info in Legal Pages */
.contact-info {
    background: var(--gray-50);
    padding: var(--spacing-6);
    border-radius: var(--radius-lg);
    margin: var(--spacing-6) 0;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-4);
}

.contact-method:last-child {
    margin-bottom: 0;
}

.contact-method i {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
}

.contact-method div {
    flex: 1;
}

.contact-method strong {
    display: block;
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.contact-method a {
    color: var(--primary-color);
    text-decoration: none;
}

.contact-method a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 1023px) {
    .toc {
        position: static;
        margin-bottom: var(--spacing-8);
    }
    
    .toc ul {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-2);
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .legal-text {
        padding: var(--spacing-6);
    }
    
    .legal-text h2 {
        font-size: var(--font-size-xl);
    }
    
    .legal-text h3 {
        font-size: var(--font-size-lg);
    }
    
    .toc ul {
        grid-template-columns: 1fr;
    }
    
    .contact-method {
        flex-direction: column;
        text-align: center;
    }
    
    .contact-method i {
        margin: 0 auto var(--spacing-2);
    }
}

/* Smooth Scrolling for Anchor Links */
html {
    scroll-behavior: smooth;
}

/* Section Targeting */
section:target {
    animation: highlight 2s ease-in-out;
}

@keyframes highlight {
    0% {
        background-color: rgba(99, 102, 241, 0.1);
    }
    100% {
        background-color: transparent;
    }
}

/* Print Styles */
@media print {
    .legal-hero,
    .footer,
    .toc {
        display: none;
    }
    
    .content-wrapper {
        grid-template-columns: 1fr;
    }
    
    .legal-text {
        box-shadow: none;
        border: none;
        padding: 0;
    }
    
    .legal-text section {
        page-break-inside: avoid;
    }
    
    .legal-text h2 {
        page-break-after: avoid;
    }
    
    .highlight-box {
        background: var(--gray-100) !important;
        color: var(--gray-900) !important;
        border: 1px solid var(--gray-300);
    }
    
    .highlight-box * {
        color: var(--gray-900) !important;
    }
}

/* Loading Animation for Smooth Scrolling */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--gray-200);
    z-index: 9999;
}

.scroll-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    width: 0%;
    transition: width 0.1s ease;
}
